#!/usr/bin/env python3
"""
查找代理设置来源工具（简化版）
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_parent_processes():
    """检查父进程链，找出可能设置代理的进程"""
    print("=" * 60)
    print("检查进程链")
    print("=" * 60)
    
    try:
        current_pid = os.getpid()
        current_process = psutil.Process(current_pid)
        
        print(f"当前进程: {current_process.name()} (PID: {current_pid})")
        
        # 遍历父进程链
        process = current_process
        level = 0
        while process.parent() is not None:
            parent = process.parent()
            level += 1
            indent = "  " * level
            
            try:
                print(f"{indent}父进程 {level}: {parent.name()} (PID: {parent.pid})")
                print(f"{indent}  命令行: {' '.join(parent.cmdline())}")
                print(f"{indent}  可执行文件: {parent.exe()}")
                
                # 检查是否是常见的代理软件
                proxy_apps = ['clash', 'v2ray', 'shadowsocks', 'proxifier', 'fiddler', 'charles']
                if any(app in parent.name().lower() for app in proxy_apps):
                    print(f"{indent}  ⚠️  可能的代理软件: {parent.name()}")
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                print(f"{indent}父进程 {level}: <无法访问>")
            
            process = parent
            
            # 防止无限循环
            if level > 10:
                break
                
    except Exception as e:
        print(f"检查进程链失败: {e}")

def check_running_proxy_processes():
    """检查当前运行的可能的代理进程"""
    print("\n" + "=" * 60)
    print("检查运行中的代理相关进程")
    print("=" * 60)
    
    proxy_keywords = [
        'clash', 'v2ray', 'shadowsocks', 'ss-local', 'ss-server',
        'proxifier', 'fiddler', 'charles', 'burp', 'mitmproxy',
        'squid', 'nginx', 'privoxy', 'polipo', 'tinyproxy',
        'wingate', 'ccproxy', 'proxycap', 'sockscap'
    ]
    
    found_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'exe']):
        try:
            proc_name = proc.info['name'].lower()
            proc_cmdline = ' '.join(proc.info['cmdline'] or []).lower()
            
            for keyword in proxy_keywords:
                if keyword in proc_name or keyword in proc_cmdline:
                    found_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': proc.info['cmdline'],
                        'exe': proc.info['exe'],
                        'keyword': keyword
                    })
                    break
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if found_processes:
        print(f"发现 {len(found_processes)} 个可能的代理进程:")
        for proc in found_processes:
            print(f"  进程: {proc['name']} (PID: {proc['pid']})")
            print(f"    匹配关键词: {proc['keyword']}")
            print(f"    可执行文件: {proc['exe']}")
            if proc['cmdline']:
                print(f"    命令行: {' '.join(proc['cmdline'])}")
            print()
    else:
        print("未发现明显的代理进程")

def check_port_33210():
    """检查端口33210被哪个进程占用"""
    print("\n" + "=" * 60)
    print("检查端口33210占用情况")
    print("=" * 60)
    
    try:
        connections = psutil.net_connections(kind='inet')
        port_33210_processes = []
        
        for conn in connections:
            if conn.laddr.port == 33210:
                try:
                    proc = psutil.Process(conn.pid)
                    port_33210_processes.append({
                        'pid': conn.pid,
                        'name': proc.name(),
                        'exe': proc.exe(),
                        'cmdline': proc.cmdline(),
                        'status': conn.status
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    port_33210_processes.append({
                        'pid': conn.pid,
                        'name': '<无法访问>',
                        'exe': '<无法访问>',
                        'cmdline': [],
                        'status': conn.status
                    })
        
        if port_33210_processes:
            print(f"端口33210被以下进程占用:")
            for proc in port_33210_processes:
                print(f"  进程: {proc['name']} (PID: {proc['pid']})")
                print(f"    状态: {proc['status']}")
                print(f"    可执行文件: {proc['exe']}")
                if proc['cmdline']:
                    print(f"    命令行: {' '.join(proc['cmdline'])}")
                print()
        else:
            print("端口33210当前未被占用")
            
    except Exception as e:
        print(f"检查端口占用失败: {e}")

def check_vscode_settings():
    """检查VSCode的代理设置"""
    print("\n" + "=" * 60)
    print("检查VSCode代理设置")
    print("=" * 60)
    
    # VSCode用户设置文件路径
    vscode_settings_paths = [
        Path.home() / "AppData/Roaming/Code/User/settings.json",
        Path.home() / "AppData/Roaming/Code - Insiders/User/settings.json",
        Path(".vscode/settings.json")
    ]
    
    for settings_path in vscode_settings_paths:
        if settings_path.exists():
            print(f"检查: {settings_path}")
            try:
                with open(settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                proxy_settings = {}
                for key, value in settings.items():
                    if 'proxy' in key.lower():
                        proxy_settings[key] = value
                
                if proxy_settings:
                    print("  发现代理相关设置:")
                    for key, value in proxy_settings.items():
                        print(f"    {key}: {value}")
                else:
                    print("  未发现代理设置")
                    
            except Exception as e:
                print(f"  读取设置文件失败: {e}")
        else:
            print(f"设置文件不存在: {settings_path}")

def check_environment_inheritance():
    """检查环境变量的继承情况"""
    print("\n" + "=" * 60)
    print("检查环境变量继承")
    print("=" * 60)
    
    # 检查当前进程的环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    print("当前进程环境变量:")
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"  {var} = {value}")
    
    # 尝试启动一个新的子进程来检查环境变量继承
    print("\n启动新的子进程检查环境变量继承:")
    try:
        result = subprocess.run([
            sys.executable, '-c', 
            'import os; ' + 
            '; '.join([f'print(f"{var}: {{os.environ.get(\'{var}\', \'未设置\')}}")' for var in proxy_vars])
        ], capture_output=True, text=True, timeout=10)
        
        print("子进程环境变量:")
        print(result.stdout)
        
    except Exception as e:
        print(f"检查子进程环境变量失败: {e}")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("解决方案建议")
    print("=" * 60)
    
    print("1. 如果是代理软件设置的环境变量:")
    print("   - 关闭代理软件的系统代理功能")
    print("   - 或者在代理软件中添加应用程序到直连列表")
    
    print("\n2. 如果是VSCode或其他IDE设置的:")
    print("   - 检查IDE的代理设置")
    print("   - 在终端中直接运行应用，而不是通过IDE")
    
    print("\n3. 临时解决方案:")
    print("   - 使用我们创建的 run_without_proxy.bat")
    print("   - 或者修改应用程序代码自动禁用代理")

def main():
    """主函数"""
    print("代理设置来源查找工具")
    print("=" * 60)
    
    check_parent_processes()
    check_running_proxy_processes()
    check_port_33210()
    check_vscode_settings()
    check_environment_inheritance()
    provide_solutions()

if __name__ == "__main__":
    main()
