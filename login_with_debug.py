#!/usr/bin/env python3
"""
带调试信息和账号记录的登录测试工具
"""

import sys
import os
import json
import getpass
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from auth.auth_manager import AuthManager

# 配置文件路径
CONFIG_FILE = "login_config.json"

def load_login_config():
    """加载登录配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
    
    return {}

def save_login_config(config):
    """保存登录配置"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"✓ 配置已保存到 {CONFIG_FILE}")
    except Exception as e:
        print(f"保存配置文件失败: {e}")

def get_login_credentials():
    """获取登录凭据"""
    config = load_login_config()
    
    print("\n" + "=" * 60)
    print("登录凭据输入")
    print("=" * 60)
    
    # 获取用户名
    saved_username = config.get('username', '')
    if saved_username:
        print(f"上次使用的用户名: {saved_username}")
        use_saved = input("是否使用上次的用户名? (y/n): ").lower().strip()
        if use_saved in ['y', 'yes', '是', '']:
            username = saved_username
        else:
            username = input("请输入用户名: ").strip()
    else:
        username = input("请输入用户名: ").strip()
    
    # 获取密码
    saved_password = config.get('password', '')
    if saved_password:
        print("检测到已保存的密码")
        use_saved = input("是否使用已保存的密码? (y/n): ").lower().strip()
        if use_saved in ['y', 'yes', '是', '']:
            password = saved_password
        else:
            password = getpass.getpass("请输入密码: ")
    else:
        password = getpass.getpass("请输入密码: ")
    
    # 询问是否保存
    save_credentials = input("是否保存用户名和密码? (y/n): ").lower().strip()
    if save_credentials in ['y', 'yes', '是']:
        config['username'] = username
        config['password'] = password
        save_login_config(config)
    
    return username, password

def debug_login_process():
    """带调试信息的登录过程"""
    print("=" * 80)
    print(f"环境采样记录检查系统 - 调试登录工具")
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 获取登录凭据
    username, password = get_login_credentials()
    
    print(f"\n📋 登录信息:")
    print(f"   用户名: {username}")
    print(f"   密码: {'*' * len(password)}")
    
    # 创建认证管理器
    print(f"\n🔧 步骤1: 初始化认证管理器")
    print("-" * 50)
    
    try:
        auth_manager = AuthManager()
        print("✓ 认证管理器创建成功")
        print(f"✓ 会话对象: {type(auth_manager.session)}")
        print(f"✓ 初始请求头: {dict(auth_manager.session.headers)}")
    except Exception as e:
        print(f"✗ 认证管理器创建失败: {e}")
        return False
    
    # 获取验证码
    print(f"\n🖼️  步骤2: 获取验证码")
    print("-" * 50)
    
    try:
        success, image_data, error_msg = auth_manager.get_captcha()
        
        if success:
            print(f"✓ 验证码获取成功")
            print(f"✓ 图片大小: {len(image_data)} 字节")
            print(f"✓ 当前Cookie: {dict(auth_manager.session.cookies)}")
            
            # 保存验证码
            captcha_file = f"captcha_{datetime.now().strftime('%H%M%S')}.jpg"
            with open(captcha_file, "wb") as f:
                f.write(image_data)
            print(f"✓ 验证码已保存为: {captcha_file}")
            
        else:
            print(f"✗ 验证码获取失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"✗ 验证码获取异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 输入验证码
    print(f"\n🔢 步骤3: 输入验证码")
    print("-" * 50)
    print(f"请查看验证码文件: {captcha_file}")
    captcha = input("请输入验证码: ").strip()
    print(f"✓ 验证码输入: {captcha}")
    
    # 执行登录
    print(f"\n🔐 步骤4: 执行登录")
    print("-" * 50)
    
    try:
        print("📤 发送登录请求...")
        print(f"   用户名: {username}")
        print(f"   密码MD5: {auth_manager._encrypt_password(password)}")
        print(f"   验证码: {captcha}")
        print(f"   登录URL: http://*************/ehscare/pms/login")
        
        success, message = auth_manager.login_with_debug(username, password, captcha)
        
        if success:
            print(f"\n✅ 登录成功!")
            print(f"✓ 消息: {message}")
            print(f"✓ 认证状态: {auth_manager.is_authenticated()}")
            print(f"✓ 用户信息: {auth_manager.get_user_info()}")
            print(f"✓ 最终Cookie: {dict(auth_manager.session.cookies)}")
            
            # 测试API调用
            test_api_functionality(auth_manager)
            return True
            
        else:
            print(f"\n❌ 登录失败!")
            print(f"✗ 错误信息: {message}")
            print(f"✗ 当前Cookie: {dict(auth_manager.session.cookies)}")
            
            # 分析失败原因
            analyze_failure(message)
            return False
            
    except Exception as e:
        print(f"\n💥 登录过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_failure(message):
    """分析登录失败原因"""
    print(f"\n🔍 失败原因分析")
    print("-" * 50)
    
    if '验证码' in message:
        print("💡 可能原因: 验证码错误")
        print("   建议: 重新获取验证码，仔细核对输入")
    elif '用户名' in message or '密码' in message:
        print("💡 可能原因: 用户名或密码错误")
        print("   建议: 检查用户名和密码是否正确")
    elif '过期' in message:
        print("💡 可能原因: 会话过期")
        print("   建议: 重新启动程序")
    elif '锁定' in message or '冻结' in message:
        print("💡 可能原因: 账户被锁定")
        print("   建议: 联系管理员解锁账户")
    else:
        print("💡 可能原因: 未知错误")
        print("   建议: 检查网络连接，稍后重试")

def test_api_functionality(auth_manager):
    """测试API功能"""
    print(f"\n🧪 步骤5: 测试API功能")
    print("-" * 50)
    
    try:
        from api.api_client import APIClient
        
        api_client = APIClient(auth_manager.get_session())
        print("✓ API客户端创建成功")
        
        # 测试年份提取
        test_keyword = "KDHJ259560"
        year = api_client.extract_year_from_keyword(test_keyword)
        print(f"✓ 年份提取测试: {test_keyword} -> {year}")
        
        # 测试项目信息获取
        print(f"📡 测试项目信息获取...")
        success, project_data, error_msg = api_client.get_project_info(test_keyword, year)
        
        if success:
            print(f"✅ 项目信息获取成功!")
            print(f"   项目编号: {project_data.get('missionNo', 'N/A')}")
            print(f"   项目ID: {project_data.get('id', 'N/A')}")
            print(f"   项目名称: {project_data.get('missionName', 'N/A')}")
        else:
            print(f"❌ 项目信息获取失败: {error_msg}")
            
    except Exception as e:
        print(f"💥 API测试异常: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        success = debug_login_process()
        
        print(f"\n" + "=" * 80)
        if success:
            print("🎉 登录测试完成 - 成功!")
            print("现在可以使用 python main.py 启动完整应用")
        else:
            print("😞 登录测试完成 - 失败!")
            print("请根据上述调试信息检查问题")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
