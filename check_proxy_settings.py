#!/usr/bin/env python3
"""
检查系统代理设置工具
"""

import os
import sys
import requests
import urllib.request
from urllib.error import URLError

def check_environment_proxy():
    """检查环境变量中的代理设置"""
    print("=" * 60)
    print("检查环境变量代理设置")
    print("=" * 60)
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    found_proxy = False
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"✓ 发现代理设置: {var} = {value}")
            found_proxy = True
        else:
            print(f"  {var}: 未设置")
    
    if not found_proxy:
        print("✓ 环境变量中未发现代理设置")
    
    return found_proxy

def check_requests_proxy():
    """检查requests库检测到的代理"""
    print("\n" + "=" * 60)
    print("检查requests库代理检测")
    print("=" * 60)
    
    try:
        # 创建一个session来检查代理
        session = requests.Session()
        
        # 尝试获取代理信息
        proxies = session.proxies
        if proxies:
            print("✓ requests检测到的代理设置:")
            for protocol, proxy in proxies.items():
                print(f"  {protocol}: {proxy}")
            return True
        else:
            print("✓ requests未检测到代理设置")
            return False
            
    except Exception as e:
        print(f"✗ 检查requests代理时出错: {e}")
        return False

def check_urllib_proxy():
    """检查urllib的代理设置"""
    print("\n" + "=" * 60)
    print("检查urllib代理设置")
    print("=" * 60)
    
    try:
        # 检查urllib的代理处理器
        proxy_handler = urllib.request.ProxyHandler()
        print(f"urllib代理处理器: {proxy_handler.proxies}")
        
        if proxy_handler.proxies:
            print("✓ urllib检测到代理设置:")
            for protocol, proxy in proxy_handler.proxies.items():
                print(f"  {protocol}: {proxy}")
            return True
        else:
            print("✓ urllib未检测到代理设置")
            return False
            
    except Exception as e:
        print(f"✗ 检查urllib代理时出错: {e}")
        return False

def test_direct_connection():
    """测试直接连接目标服务器"""
    print("\n" + "=" * 60)
    print("测试直接连接")
    print("=" * 60)
    
    target_url = "http://106.12.16.234/"
    
    # 测试1: 使用requests，禁用代理
    print("测试1: 使用requests（禁用代理）")
    try:
        session = requests.Session()
        session.proxies = {}  # 显式禁用代理
        response = session.get(target_url, timeout=10)
        print(f"✓ 直接连接成功: {response.status_code}")
        return True
    except Exception as e:
        print(f"✗ 直接连接失败: {e}")
    
    # 测试2: 使用urllib，禁用代理
    print("\n测试2: 使用urllib（禁用代理）")
    try:
        # 创建不使用代理的opener
        no_proxy_handler = urllib.request.ProxyHandler({})
        opener = urllib.request.build_opener(no_proxy_handler)
        response = opener.open(target_url, timeout=10)
        print(f"✓ urllib直接连接成功: {response.getcode()}")
        return True
    except Exception as e:
        print(f"✗ urllib直接连接失败: {e}")
    
    return False

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("解决方案")
    print("=" * 60)
    
    print("方案1: 临时禁用环境变量代理")
    print("在命令行中运行以下命令:")
    print("  set HTTP_PROXY=")
    print("  set HTTPS_PROXY=")
    print("  set http_proxy=")
    print("  set https_proxy=")
    print("  python main.py")
    
    print("\n方案2: 修改Windows系统代理设置")
    print("1. 按 Win + I 打开设置")
    print("2. 点击 网络和Internet")
    print("3. 点击 代理")
    print("4. 关闭 '使用代理服务器' 选项")
    
    print("\n方案3: 使用修复后的应用程序")
    print("我可以修改应用程序代码，让它自动禁用代理")

def main():
    """主函数"""
    print("系统代理设置检查工具")
    print("=" * 60)
    
    # 检查各种代理设置
    env_proxy = check_environment_proxy()
    requests_proxy = check_requests_proxy()
    urllib_proxy = check_urllib_proxy()
    
    # 测试直接连接
    direct_ok = test_direct_connection()
    
    # 总结
    print("\n" + "=" * 60)
    print("检查结果总结")
    print("=" * 60)
    
    if env_proxy or requests_proxy or urllib_proxy:
        print("❌ 发现代理设置，这可能是连接问题的原因")
        if direct_ok:
            print("✅ 但是禁用代理后可以正常连接")
        else:
            print("❌ 即使禁用代理也无法连接，可能还有其他问题")
    else:
        print("✅ 未发现明显的代理设置")
        if not direct_ok:
            print("❌ 但是连接仍然失败，可能是网络或服务器问题")
    
    # 提供解决方案
    provide_solutions()

if __name__ == "__main__":
    main()
