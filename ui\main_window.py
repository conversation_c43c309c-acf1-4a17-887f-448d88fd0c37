"""
主窗口 - 应用程序主界面
"""

import os
from datetime import datetime
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QGroupBox, QFormLayout, QLineEdit, QPushButton,
                            QTextEdit, QProgressBar, QLabel, QMessageBox,
                            QFileDialog, QTabWidget, QTableWidget, QTableWidgetItem,
                            QHeaderView, QSplitter, QDialog)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QAction

from config import APP_NAME, WINDOW_WIDTH, WINDOW_HEIGHT
from ui.login_dialog import LoginDialog
from api.api_client import APIClient
from data.data_processor import DataProcessor
from reports.report_generator import ReportGenerator


class DataProcessingThread(QThread):
    """数据处理线程"""
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态信息
    data_ready = pyqtSignal(bool, str, object, object)  # 成功标志, 消息, 时间线数据, 冲突列表
    
    def __init__(self, api_client: APIClient, project_keyword: str):
        super().__init__()
        self.api_client = api_client
        self.project_keyword = project_keyword
        self.data_processor = DataProcessor()
    
    def run(self):
        """执行数据处理"""
        try:
            # 步骤1: 提取年份并获取项目信息
            self.progress_updated.emit(10, "正在提取年份信息...")
            year = self.api_client.extract_year_from_keyword(self.project_keyword)
            if not year:
                self.data_ready.emit(False, "无法从项目编号中提取年份", None, None)
                return
            
            self.progress_updated.emit(20, f"正在获取项目信息 (年份: {year})...")
            success, project_data, error_msg = self.api_client.get_project_info(self.project_keyword, year)
            if not success:
                self.data_ready.emit(False, f"获取项目信息失败: {error_msg}", None, None)
                return
            
            project_id = project_data.get('id') or project_data.get('missBillId')
            if not project_id:
                self.data_ready.emit(False, "项目数据中缺少ID字段", None, None)
                return
            
            # 步骤2: 获取采样记录清单
            self.progress_updated.emit(40, "正在获取采样记录清单...")
            success, sampling_records, error_msg = self.api_client.get_sampling_records(str(project_id))
            if not success:
                self.data_ready.emit(False, f"获取采样记录失败: {error_msg}", None, None)
                return
            
            if not sampling_records:
                self.data_ready.emit(False, "未找到采样记录", None, None)
                return
            
            # 步骤3: 获取详细采样信息
            detailed_records = []
            total_records = len(sampling_records)
            
            for i, record in enumerate(sampling_records):
                progress = 40 + int((i / total_records) * 40)
                self.progress_updated.emit(progress, f"正在获取详细信息 ({i+1}/{total_records})...")
                
                save_unique_key = record.get('saveUniqueKey')
                if not save_unique_key:
                    continue
                
                success, detail_data, error_msg = self.api_client.get_sampling_detail(save_unique_key)
                if success and detail_data:
                    # 合并基本信息和详细信息
                    combined_data = {**record, **detail_data}
                    detailed_records.append(combined_data)
            
            if not detailed_records:
                self.data_ready.emit(False, "未获取到有效的详细采样信息", None, None)
                return
            
            # 步骤4: 数据解析和冲突检查
            self.progress_updated.emit(85, "正在解析采样数据...")
            parsed_records = self.data_processor.parse_sampling_data(detailed_records)
            
            if not parsed_records:
                self.data_ready.emit(False, "数据解析失败，未找到有效记录", None, None)
                return
            
            self.progress_updated.emit(90, "正在检查时间冲突...")
            personnel_conflicts = self.data_processor.check_personnel_conflicts(parsed_records)
            equipment_conflicts = self.data_processor.check_equipment_conflicts(parsed_records)
            all_conflicts = personnel_conflicts + equipment_conflicts
            
            self.progress_updated.emit(95, "正在生成时间线数据...")
            timeline_data = self.data_processor.generate_timeline(parsed_records)
            
            self.progress_updated.emit(100, "数据处理完成")
            self.data_ready.emit(True, f"成功处理 {len(parsed_records)} 条记录，发现 {len(all_conflicts)} 个冲突", 
                               timeline_data, all_conflicts)
            
        except Exception as e:
            self.data_ready.emit(False, f"数据处理过程中发生错误: {str(e)}", None, None)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.auth_manager = None
        self.api_client = None
        self.processing_thread = None
        self.timeline_data = None
        self.conflicts = None
        
        self.init_ui()
        # 延迟显示登录对话框，等主窗口显示后再显示
        # self.show_login_dialog() 移到 showEvent 中
        self.login_dialog_shown = False
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(APP_NAME)
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：输入和控制区域
        control_widget = self.create_control_panel()
        splitter.addWidget(control_widget)
        
        # 下半部分：结果显示区域
        result_widget = self.create_result_panel()
        splitter.addWidget(result_widget)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 3)
        
        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)
        
        # 状态栏
        self.statusBar().showMessage("请先登录系统")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        export_action = QAction('导出报告', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_report)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 项目信息组
        project_group = QGroupBox("项目信息")
        project_layout = QFormLayout()
        
        self.project_keyword_edit = QLineEdit()
        self.project_keyword_edit.setPlaceholderText("请输入项目编号，如：KDHJ259560")
        project_layout.addRow("项目编号:", self.project_keyword_edit)
        
        project_group.setLayout(project_layout)
        layout.addWidget(project_group)
        
        # 操作按钮组
        button_layout = QHBoxLayout()
        
        self.analyze_btn = QPushButton("开始分析")
        self.analyze_btn.clicked.connect(self.start_analysis)
        self.analyze_btn.setEnabled(False)
        button_layout.addWidget(self.analyze_btn)
        
        self.export_btn = QPushButton("导出报告")
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setEnabled(False)
        button_layout.addWidget(self.export_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态信息
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
        widget.setLayout(layout)
        return widget

    def create_result_panel(self) -> QWidget:
        """创建结果显示面板"""
        # 创建标签页控件
        tab_widget = QTabWidget()

        # 冲突分析标签页
        self.conflicts_table = QTableWidget()
        tab_widget.addTab(self.conflicts_table, "冲突分析")

        # 人员时间流标签页
        self.personnel_table = QTableWidget()
        tab_widget.addTab(self.personnel_table, "人员时间流")

        # 设备时间流标签页
        self.equipment_table = QTableWidget()
        tab_widget.addTab(self.equipment_table, "设备时间流")

        # 统计汇总标签页
        self.summary_table = QTableWidget()
        tab_widget.addTab(self.summary_table, "统计汇总")

        return tab_widget

    def show_login_dialog(self):
        """显示登录对话框"""
        print("\n🚀 [MAIN] 显示登录对话框...")
        login_dialog = LoginDialog(self, debug_mode=True)  # 启用调试模式

        if login_dialog.exec() == QDialog.DialogCode.Accepted:
            print("🎉 [MAIN] 登录对话框返回成功")

            self.auth_manager = login_dialog.get_auth_manager()
            self.api_client = APIClient(self.auth_manager.get_session(), debug_mode=True)  # 启用调试模式

            print(f"🎉 [MAIN] API客户端初始化完成")
            print(f"🎉 [MAIN] 当前用户: {self.auth_manager.get_user_info().get('staffName', 'Unknown')}")

            # 启用界面
            self.analyze_btn.setEnabled(True)
            self.statusBar().showMessage("登录成功，可以开始分析")
            self.add_status_message("系统登录成功")

            print("✅ [MAIN] 主界面初始化完成，可以开始使用")
        else:
            print("❌ [MAIN] 用户取消登录，退出应用")
            # 用户取消登录，退出应用
            self.close()

    def start_analysis(self):
        """开始数据分析"""
        project_keyword = self.project_keyword_edit.text().strip()

        if not project_keyword:
            QMessageBox.warning(self, "输入错误", "请输入项目编号")
            return

        if not self.auth_manager or not self.auth_manager.is_authenticated():
            QMessageBox.warning(self, "认证错误", "请先登录系统")
            self.show_login_dialog()
            return

        # 禁用界面
        self.analyze_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        self.add_status_message(f"开始分析项目: {project_keyword}")

        # 启动数据处理线程
        self.processing_thread = DataProcessingThread(self.api_client, project_keyword)
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.data_ready.connect(self.on_data_ready)
        self.processing_thread.start()

    def update_progress(self, value: int, message: str):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.add_status_message(message)

    def on_data_ready(self, success: bool, message: str, timeline_data, conflicts):
        """数据处理完成"""
        self.analyze_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        if success:
            self.timeline_data = timeline_data
            self.conflicts = conflicts
            self.export_btn.setEnabled(True)

            # 更新显示
            self.update_result_display()
            self.add_status_message(f"分析完成: {message}")
            self.statusBar().showMessage("分析完成，可以导出报告")
        else:
            self.add_status_message(f"分析失败: {message}")
            QMessageBox.critical(self, "分析失败", message)

    def update_result_display(self):
        """更新结果显示"""
        if not self.timeline_data or not self.conflicts:
            return

        try:
            # 生成报告数据
            report_generator = ReportGenerator()

            # 更新冲突分析表格
            conflicts_df = report_generator.create_conflict_report(self.conflicts)
            self.populate_table(self.conflicts_table, conflicts_df)

            # 更新人员时间流表格
            personnel_df = report_generator.create_personnel_timeline_report(self.timeline_data, self.conflicts)
            self.populate_table(self.personnel_table, personnel_df)

            # 更新设备时间流表格
            equipment_df = report_generator.create_equipment_timeline_report(self.timeline_data, self.conflicts)
            self.populate_table(self.equipment_table, equipment_df)

            # 更新统计汇总表格
            summary_df = report_generator.create_summary_report(self.timeline_data, self.conflicts)
            self.populate_table(self.summary_table, summary_df)

        except Exception as e:
            self.add_status_message(f"更新显示时出错: {str(e)}")

    def populate_table(self, table: QTableWidget, df):
        """填充表格数据"""
        if df.empty:
            table.setRowCount(0)
            table.setColumnCount(0)
            return

        # 设置表格大小
        table.setRowCount(len(df))
        table.setColumnCount(len(df.columns))

        # 设置表头
        table.setHorizontalHeaderLabels(df.columns.tolist())

        # 填充数据
        for i in range(len(df)):
            for j in range(len(df.columns)):
                item = QTableWidgetItem(str(df.iloc[i, j]))

                # 冲突标识高亮
                if '冲突标识' in df.columns and df.columns[j] == '冲突标识' and df.iloc[i, j] == '是':
                    item.setBackground(Qt.GlobalColor.red)
                elif '严重程度' in df.columns and df.columns[j] == '严重程度':
                    if df.iloc[i, j] == '严重':
                        item.setBackground(Qt.GlobalColor.red)
                    elif df.iloc[i, j] == '警告':
                        item.setBackground(Qt.GlobalColor.yellow)

                table.setItem(i, j, item)

        # 自动调整列宽
        table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

    def export_report(self):
        """导出报告"""
        if not self.timeline_data or not self.conflicts:
            QMessageBox.warning(self, "导出错误", "没有可导出的数据，请先进行分析")
            return

        # 选择保存文件
        default_filename = f"环境采样记录检查报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存报告", default_filename, "Excel文件 (*.xlsx)"
        )

        if not filename:
            return

        try:
            # 生成报告
            report_generator = ReportGenerator()
            success = report_generator.export_to_excel(self.timeline_data, self.conflicts, filename)

            if success:
                QMessageBox.information(self, "导出成功", f"报告已保存到:\n{filename}")
                self.add_status_message(f"报告导出成功: {filename}")
            else:
                QMessageBox.critical(self, "导出失败", "报告导出失败，请检查文件路径和权限")

        except Exception as e:
            QMessageBox.critical(self, "导出错误", f"导出过程中发生错误:\n{str(e)}")

    def add_status_message(self, message: str):
        """添加状态消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.status_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_text.setTextCursor(cursor)

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         f"{APP_NAME}\n\n"
                         "版本: 1.0.0\n"
                         "功能: 环境采样记录时间冲突检查\n\n"
                         "开发: AI Assistant")

    def showEvent(self, event):
        """窗口显示事件处理"""
        super().showEvent(event)

        # 在主窗口显示后显示登录对话框
        if not self.login_dialog_shown:
            self.login_dialog_shown = True
            # 使用QTimer延迟显示，确保主窗口完全显示
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(100, self.show_login_dialog)

    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止正在运行的线程
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()

        event.accept()
