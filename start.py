#!/usr/bin/env python3
"""
启动脚本 - 自动检查依赖并启动应用
"""

import sys
import subprocess
import importlib

def check_and_install_dependencies():
    """检查并安装依赖"""
    required_packages = {
        'PyQt6': 'PyQt6',
        'requests': 'requests',
        'openpyxl': 'openpyxl',
        'pandas': 'pandas',
        'dateutil': 'python-dateutil'
    }
    
    missing_packages = []
    
    print("检查依赖包...")
    for module_name, package_name in required_packages.items():
        try:
            importlib.import_module(module_name)
            print(f"✓ {package_name}")
        except ImportError:
            print(f"✗ {package_name} (缺失)")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n发现缺失的依赖包: {', '.join(missing_packages)}")
        
        response = input("是否自动安装缺失的依赖包? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("正在安装依赖包...")
            try:
                # 尝试使用清华镜像源
                cmd = [sys.executable, '-m', 'pip', 'install', 
                       '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple/'] + missing_packages
                subprocess.check_call(cmd)
                print("✓ 依赖包安装完成")
                return True
            except subprocess.CalledProcessError:
                print("✗ 依赖包安装失败，请手动安装:")
                print(f"pip install {' '.join(missing_packages)}")
                return False
        else:
            print("请手动安装依赖包后再运行应用:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def start_application():
    """启动应用程序"""
    try:
        print("\n启动环境采样记录检查系统...")
        import main
        main.main()
    except ImportError as e:
        print(f"✗ 启动失败，缺少依赖: {e}")
        print("请运行: python test_core_functionality.py 进行诊断")
        return False
    except Exception as e:
        print(f"✗ 应用程序启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("环境采样记录检查系统 - 启动器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("✗ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return
    
    print(f"✓ Python版本: {sys.version.split()[0]}")
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("\n依赖检查失败，无法启动应用")
        input("按回车键退出...")
        return
    
    # 启动应用
    if not start_application():
        print("\n应用启动失败")
        input("按回车键退出...")
        return
    
    print("应用已关闭")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n启动器发生错误: {e}")
        input("按回车键退出...")
