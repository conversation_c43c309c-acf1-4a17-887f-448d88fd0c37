# 环境采样记录检查系统 - PowerShell启动脚本

Write-Host "========================================" -ForegroundColor Green
Write-Host "环境采样记录检查系统 - 启动脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& ".\venv\Scripts\Activate.ps1"

Write-Host "启动应用程序..." -ForegroundColor Yellow
python main.py

Write-Host "应用程序已关闭" -ForegroundColor Green
Read-Host "按回车键退出"
