"""
报告生成器 - Excel报告生成和冲突标识
"""

import os
from datetime import datetime
from typing import Dict, List, Optional
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

from config import REPORT_SHEET_NAMES
from data.data_processor import SamplingRecord, ConflictInfo


class ReportGenerator:
    """报告生成器类"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.workbook = None
        self.styles = self._create_styles()
    
    def _create_styles(self) -> Dict:
        """创建Excel样式"""
        return {
            'header': {
                'font': Font(bold=True, color='FFFFFF'),
                'fill': PatternFill(start_color='366092', end_color='366092', fill_type='solid'),
                'alignment': Alignment(horizontal='center', vertical='center'),
                'border': Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            },
            'conflict_high': {
                'fill': PatternFill(start_color='FF6B6B', end_color='FF6B6B', fill_type='solid'),
                'font': Font(color='FFFFFF', bold=True)
            },
            'conflict_medium': {
                'fill': PatternFill(start_color='FFE66D', end_color='FFE66D', fill_type='solid'),
                'font': Font(color='000000')
            },
            'normal': {
                'alignment': Alignment(horizontal='left', vertical='center'),
                'border': Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            }
        }
    
    def create_personnel_timeline_report(self, timeline_data: Dict, conflicts: List[ConflictInfo]) -> pd.DataFrame:
        """
        创建人员时间流报告
        
        Args:
            timeline_data: 时间线数据
            conflicts: 冲突信息列表
            
        Returns:
            pd.DataFrame: 人员时间流报告数据
        """
        personnel_timeline = timeline_data.get('personnel_timeline', {})
        
        report_data = []
        conflict_records = self._get_conflict_record_ids(conflicts, 'personnel')
        
        for person, person_data in personnel_timeline.items():
            for date, activities in person_data.items():
                # 按时间排序
                activities.sort(key=lambda x: x.get('start_time', ''))
                
                for activity in activities:
                    # 检查是否存在冲突
                    is_conflict = any(
                        activity['sample_id'] in conflict_records.get(person, set())
                        for conflict in conflicts
                        if conflict.type.startswith('personnel')
                    )
                    
                    report_data.append({
                        '人员姓名': person,
                        '采样日期': date,
                        '开始时间': activity.get('start_time', ''),
                        '结束时间': activity.get('end_time', ''),
                        '采样点位': activity.get('site_name', ''),
                        '检测因子': activity.get('detection_factors', ''),
                        '使用设备': activity.get('equipment_id', ''),
                        '样品编号': activity.get('sample_id', ''),
                        '冲突标识': '是' if is_conflict else '否'
                    })
        
        return pd.DataFrame(report_data)
    
    def create_equipment_timeline_report(self, timeline_data: Dict, conflicts: List[ConflictInfo]) -> pd.DataFrame:
        """
        创建设备时间流报告
        
        Args:
            timeline_data: 时间线数据
            conflicts: 冲突信息列表
            
        Returns:
            pd.DataFrame: 设备时间流报告数据
        """
        equipment_timeline = timeline_data.get('equipment_timeline', {})
        
        report_data = []
        conflict_records = self._get_conflict_record_ids(conflicts, 'equipment')
        
        for equipment, equipment_data in equipment_timeline.items():
            for date, activities in equipment_data.items():
                # 按时间排序
                activities.sort(key=lambda x: x.get('start_time', ''))
                
                for activity in activities:
                    # 检查是否存在冲突
                    is_conflict = any(
                        activity['sample_id'] in conflict_records.get(equipment, set())
                        for conflict in conflicts
                        if conflict.type.startswith('equipment')
                    )
                    
                    report_data.append({
                        '设备编号': equipment,
                        '采样日期': date,
                        '开始时间': activity.get('start_time', ''),
                        '结束时间': activity.get('end_time', ''),
                        '采样点位': activity.get('site_name', ''),
                        '检测因子': activity.get('detection_factors', ''),
                        '操作人员': activity.get('personnel', ''),
                        '样品编号': activity.get('sample_id', ''),
                        '冲突标识': '是' if is_conflict else '否'
                    })
        
        return pd.DataFrame(report_data)
    
    def create_conflict_report(self, conflicts: List[ConflictInfo]) -> pd.DataFrame:
        """
        创建冲突分析报告
        
        Args:
            conflicts: 冲突信息列表
            
        Returns:
            pd.DataFrame: 冲突报告数据
        """
        report_data = []
        
        for i, conflict in enumerate(conflicts, 1):
            conflict_type_map = {
                'personnel_overlap': '人员时间重叠',
                'equipment_overlap': '设备时间重叠',
                'insufficient_transfer_time': '转移时间不足'
            }
            
            # 获取相关记录信息
            record_info = []
            for record in conflict.records:
                info = f"{record.sample_id} ({record.site_name})"
                if record.start_time and record.end_time:
                    info += f" {record.start_time.strftime('%H:%M')}-{record.end_time.strftime('%H:%M')}"
                record_info.append(info)
            
            report_data.append({
                '序号': i,
                '冲突类型': conflict_type_map.get(conflict.type, conflict.type),
                '严重程度': conflict.severity,
                '冲突描述': conflict.description,
                '涉及记录': ' | '.join(record_info),
                '样品编号': ', '.join([r.sample_id for r in conflict.records]),
                '发现时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return pd.DataFrame(report_data)
    
    def create_summary_report(self, timeline_data: Dict, conflicts: List[ConflictInfo]) -> pd.DataFrame:
        """
        创建汇总统计报告
        
        Args:
            timeline_data: 时间线数据
            conflicts: 冲突信息列表
            
        Returns:
            pd.DataFrame: 汇总报告数据
        """
        daily_summary = timeline_data.get('daily_summary', {})
        
        # 统计冲突数量
        conflict_stats = {
            'personnel_overlap': 0,
            'equipment_overlap': 0,
            'insufficient_transfer_time': 0
        }
        
        for conflict in conflicts:
            if conflict.type in conflict_stats:
                conflict_stats[conflict.type] += 1
        
        # 生成汇总数据
        summary_data = []
        
        # 总体统计
        total_personnel = len(timeline_data.get('personnel_timeline', {}))
        total_equipment = len(timeline_data.get('equipment_timeline', {}))
        total_samples = sum(day_data.get('sample_count', 0) for day_data in daily_summary.values())
        
        summary_data.extend([
            {'统计项目': '总人员数量', '数值': total_personnel, '单位': '人'},
            {'统计项目': '总设备数量', '数值': total_equipment, '单位': '台'},
            {'统计项目': '总样品数量', '数值': total_samples, '单位': '个'},
            {'统计项目': '总冲突数量', '数值': len(conflicts), '单位': '个'},
            {'统计项目': '人员时间重叠', '数值': conflict_stats['personnel_overlap'], '单位': '个'},
            {'统计项目': '设备时间重叠', '数值': conflict_stats['equipment_overlap'], '单位': '个'},
            {'统计项目': '转移时间不足', '数值': conflict_stats['insufficient_transfer_time'], '单位': '个'}
        ])
        
        # 每日统计
        for date, day_data in sorted(daily_summary.items()):
            summary_data.append({
                '统计项目': f'{date} - 人员数量',
                '数值': day_data.get('personnel_count', 0),
                '单位': '人'
            })
            summary_data.append({
                '统计项目': f'{date} - 设备数量',
                '数值': day_data.get('equipment_count', 0),
                '单位': '台'
            })
            summary_data.append({
                '统计项目': f'{date} - 样品数量',
                '数值': day_data.get('sample_count', 0),
                '单位': '个'
            })
        
        return pd.DataFrame(summary_data)
    
    def _get_conflict_record_ids(self, conflicts: List[ConflictInfo], resource_type: str) -> Dict[str, set]:
        """获取冲突记录ID映射"""
        conflict_records = {}
        
        for conflict in conflicts:
            if not conflict.type.startswith(resource_type):
                continue
                
            for record in conflict.records:
                if resource_type == 'personnel':
                    for person in record.personnel:
                        if person not in conflict_records:
                            conflict_records[person] = set()
                        conflict_records[person].add(record.sample_id)
                elif resource_type == 'equipment' and record.equipment_id:
                    if record.equipment_id not in conflict_records:
                        conflict_records[record.equipment_id] = set()
                    conflict_records[record.equipment_id].add(record.sample_id)
        
        return conflict_records
    
    def export_to_excel(self, timeline_data: Dict, conflicts: List[ConflictInfo], filename: str) -> bool:
        """
        导出Excel报告文件
        
        Args:
            timeline_data: 时间线数据
            conflicts: 冲突信息列表
            filename: 输出文件名
            
        Returns:
            bool: 导出是否成功
        """
        try:
            # 创建工作簿
            self.workbook = Workbook()
            
            # 删除默认工作表
            self.workbook.remove(self.workbook.active)
            
            # 生成各个报告
            reports = {
                REPORT_SHEET_NAMES['personnel_timeline']: self.create_personnel_timeline_report(timeline_data, conflicts),
                REPORT_SHEET_NAMES['equipment_timeline']: self.create_equipment_timeline_report(timeline_data, conflicts),
                REPORT_SHEET_NAMES['conflicts']: self.create_conflict_report(conflicts),
                REPORT_SHEET_NAMES['summary']: self.create_summary_report(timeline_data, conflicts)
            }
            
            # 创建工作表并写入数据
            for sheet_name, df in reports.items():
                if df.empty:
                    continue
                    
                ws = self.workbook.create_sheet(title=sheet_name)
                
                # 写入数据
                for r in dataframe_to_rows(df, index=False, header=True):
                    ws.append(r)
                
                # 应用样式
                self._apply_sheet_styles(ws, sheet_name, df)
            
            # 保存文件
            self.workbook.save(filename)
            return True
            
        except Exception as e:
            print(f"导出Excel文件失败: {e}")
            return False
    
    def _apply_sheet_styles(self, worksheet, sheet_name: str, df: pd.DataFrame):
        """应用工作表样式"""
        # 设置标题行样式
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            for style_name, style_value in self.styles['header'].items():
                setattr(cell, style_name, style_value)
        
        # 设置数据行样式
        for row in range(2, len(df) + 2):
            for col in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=row, column=col)
                
                # 应用基本样式
                for style_name, style_value in self.styles['normal'].items():
                    setattr(cell, style_name, style_value)
                
                # 冲突标识高亮
                if sheet_name in [REPORT_SHEET_NAMES['personnel_timeline'], REPORT_SHEET_NAMES['equipment_timeline']]:
                    if '冲突标识' in df.columns:
                        conflict_col = df.columns.get_loc('冲突标识') + 1
                        if col == conflict_col and cell.value == '是':
                            for style_name, style_value in self.styles['conflict_high'].items():
                                setattr(cell, style_name, style_value)
                
                # 冲突报告严重程度高亮
                elif sheet_name == REPORT_SHEET_NAMES['conflicts']:
                    if '严重程度' in df.columns:
                        severity_col = df.columns.get_loc('严重程度') + 1
                        if col == severity_col:
                            if cell.value == '严重':
                                for style_name, style_value in self.styles['conflict_high'].items():
                                    setattr(cell, style_name, style_value)
                            elif cell.value == '警告':
                                for style_name, style_value in self.styles['conflict_medium'].items():
                                    setattr(cell, style_name, style_value)
        
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
