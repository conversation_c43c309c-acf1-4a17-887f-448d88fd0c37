#!/usr/bin/env python3
"""
测试无代理连接
"""

import os
import requests

def test_connection():
    """测试连接"""
    print("=" * 60)
    print("测试无代理连接")
    print("=" * 60)
    
    # 显示当前环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    print("当前环境变量:")
    for var in proxy_vars:
        value = os.environ.get(var, "未设置")
        print(f"  {var}: {value}")
    
    print("\n开始测试连接...")
    
    try:
        # 创建session并显式禁用代理
        session = requests.Session()
        session.proxies = {}
        
        # 测试连接
        response = session.get("http://106.12.16.234/", timeout=10)
        print(f"✅ 连接成功! 状态码: {response.status_code}")
        
        # 测试验证码获取
        captcha_response = session.get("http://106.12.16.234/ehscare/system/verifycode.jpg", timeout=10)
        print(f"✅ 验证码获取成功! 状态码: {captcha_response.status_code}, 大小: {len(captcha_response.content)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

if __name__ == "__main__":
    test_connection()
