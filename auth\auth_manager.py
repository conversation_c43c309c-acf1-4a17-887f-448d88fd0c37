"""
认证管理器 - 处理登录、验证码、<PERSON><PERSON>管理
"""

import hashlib
import time
import requests
from typing import Optional, <PERSON>ple
import json

from config import (
    BASE_URL, CAPTCHA_URL, LOGIN_URL, REQUEST_HEADERS
)


class AuthManager:
    """认证管理器类"""
    
    def __init__(self, debug_mode=False):
        """初始化认证管理器"""
        self.session = requests.Session()
        self.debug_mode = debug_mode  # 调试模式标志

        # 检查并处理代理设置
        self._handle_proxy_settings()

        # 设置更完整的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        self.is_logged_in = False
        self.user_info = {}

        if self.debug_mode:
            print("🔧 [AUTH] 认证管理器初始化完成")
            print(f"🔧 [AUTH] 请求头: {dict(self.session.headers)}")

    def _ensure_session_initialized(self):
        """确保会话已初始化（在需要时调用）"""
        if not self.session.cookies:
            try:
                # 步骤1: 访问主页获取基础Cookie
                if self.debug_mode:
                    print("🔄 [AUTH] 正在初始化会话...")

                main_page_response = self.session.get(f"{BASE_URL}/", timeout=10)

                if self.debug_mode:
                    print(f"✓ [AUTH] 访问主页: {main_page_response.status_code}")
                    print(f"✓ [AUTH] 主页Cookie: {dict(self.session.cookies)}")

                # 步骤2: 访问登录页面获取更多Cookie
                login_page_response = self.session.get(f"{BASE_URL}/ehscare/", timeout=10)
                if self.debug_mode:
                    if login_page_response.status_code == 200:
                        print(f"✓ [AUTH] 访问登录页面: {login_page_response.status_code}")
                    else:
                        print(f"⚠️ [AUTH] 登录页面访问失败: {login_page_response.status_code}")

                # 步骤3: 访问验证码端点获取JSESSIONID
                timestamp = int(time.time() * 1000)
                captcha_url = f"{CAPTCHA_URL}?t={timestamp}"
                captcha_response = self.session.get(captcha_url, timeout=10)
                captcha_response.raise_for_status()

                if self.debug_mode:
                    print(f"✓ [AUTH] 会话初始化完成，最终Cookie: {dict(self.session.cookies)}")

            except Exception as e:
                if self.debug_mode:
                    print(f"❌ [AUTH] 会话初始化失败: {e}")
                # 即使失败也继续，因为验证码获取可能仍然有效

    def get_captcha(self) -> Tuple[bool, bytes, str]:
        """
        获取验证码图片

        Returns:
            Tuple[bool, bytes, str]: (成功标志, 图片数据, 错误信息)
        """
        try:
            if self.debug_mode:
                print("🖼️ [AUTH] 开始获取验证码...")

            # 确保会话已初始化
            self._ensure_session_initialized()

            # 添加时间戳和尺寸参数（根据示例代码）
            timestamp = int(time.time() * 1000)
            params = {
                "width": 180,
                "height": 40,
                "t": timestamp
            }

            # 设置验证码请求头（根据示例代码）
            captcha_headers = self.session.headers.copy()
            captcha_headers.update({
                "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
                "Referer": f"{BASE_URL}/",
            })

            if self.debug_mode:
                print(f"🖼️ [AUTH] 验证码URL: {CAPTCHA_URL}")
                print(f"🖼️ [AUTH] 验证码参数: {params}")

            response = self.session.get(CAPTCHA_URL, params=params, headers=captcha_headers, timeout=10)
            response.raise_for_status()

            if self.debug_mode:
                print(f"✓ [AUTH] 验证码获取成功，大小: {len(response.content)} 字节")
                print(f"✓ [AUTH] 当前Cookie: {dict(self.session.cookies)}")

            return True, response.content, ""

        except requests.exceptions.RequestException as e:
            error_msg = f"获取验证码失败: {str(e)}"
            if self.debug_mode:
                print(f"❌ [AUTH] {error_msg}")
            return False, b"", error_msg
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            if self.debug_mode:
                print(f"❌ [AUTH] {error_msg}")
            return False, b"", error_msg
    
    def _encrypt_password(self, password: str) -> str:
        """
        使用MD5加密密码
        
        Args:
            password: 原始密码
            
        Returns:
            str: MD5加密后的密码
        """
        return hashlib.md5(password.encode('utf-8')).hexdigest()
    
    def login(self, username: str, password: str, captcha: str) -> Tuple[bool, str]:
        """
        执行登录操作
        
        Args:
            username: 用户名
            password: 密码
            captcha: 验证码
            
        Returns:
            Tuple[bool, str]: (成功标志, 错误信息)
        """
        try:
            if self.debug_mode:
                print("🔐 [AUTH] 开始执行登录...")

            # 准备登录数据（根据示例代码修正字段名）
            password_md5 = self._encrypt_password(password)
            login_data = {
                "userName": username,      # 注意：使用 userName 而不是 username
                "password": password_md5,
                "verifyCode": captcha,     # 注意：使用 verifyCode 而不是 verifycode
                "autoLogin": False         # 添加 autoLogin 字段
            }

            if self.debug_mode:
                print(f"🔐 [AUTH] 登录数据:")
                print(f"   用户名: {username}")
                print(f"   密码MD5: {password_md5}")
                print(f"   验证码: {captcha}")

            # 设置请求头，模拟真实浏览器请求
            headers = self.session.headers.copy()
            headers.update({
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': BASE_URL,
                'Referer': f'{BASE_URL}/',
                'Host': '*************'
            })

            if self.debug_mode:
                print(f"🔐 [AUTH] 登录URL: {LOGIN_URL}")
                print(f"🔐 [AUTH] 当前Cookie: {dict(self.session.cookies)}")

            # 发送登录请求
            response = self.session.post(
                LOGIN_URL,
                json=login_data,
                headers=headers,
                timeout=15
            )
            response.raise_for_status()

            if self.debug_mode:
                print(f"📨 [AUTH] 登录响应状态码: {response.status_code}")
                print(f"📨 [AUTH] 响应Cookie: {dict(response.cookies)}")
                print(f"📨 [AUTH] 响应内容: {response.text}")

            # 解析响应
            result = response.json()

            # 检查响应格式：{rc: 0, ret: {...}, err: null}
            rc = result.get('rc', -1)
            ret = result.get('ret', {})
            err = result.get('err', None)

            if self.debug_mode:
                print(f"📋 [AUTH] 响应解析:")
                print(f"   返回码(rc): {rc}")
                print(f"   错误信息(err): {err}")
                print(f"   用户数据(ret): {ret}")

            if rc == 0 and err is None:
                # 登录成功，提取用户信息
                self.is_logged_in = True
                self.user_info = ret

                if self.debug_mode:
                    print(f"✅ [AUTH] 登录成功!")
                    print(f"✅ [AUTH] 用户信息: {ret}")
                    print(f"✅ [AUTH] 最终Cookie: {dict(self.session.cookies)}")

                # 检查基本的JSESSIONID Cookie是否存在
                if 'JSESSIONID' not in self.session.cookies:
                    error_msg = "登录成功但缺少会话Cookie"
                    if self.debug_mode:
                        print(f"❌ [AUTH] {error_msg}")
                    return False, error_msg

                return True, "登录成功"
            else:
                # 登录失败，提取错误信息
                error_msg = err if err else f"登录失败 (rc: {rc})"
                if self.debug_mode:
                    print(f"❌ [AUTH] 登录失败: {error_msg}")
                return False, error_msg
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            if self.debug_mode:
                print(f"❌ [AUTH] {error_msg}")
            return False, error_msg
        except json.JSONDecodeError:
            error_msg = "服务器响应格式错误"
            if self.debug_mode:
                print(f"❌ [AUTH] {error_msg}")
            return False, error_msg
        except Exception as e:
            error_msg = f"登录过程中发生错误: {str(e)}"
            if self.debug_mode:
                print(f"❌ [AUTH] {error_msg}")
                import traceback
                traceback.print_exc()
            return False, error_msg
    
    def get_session(self) -> requests.Session:
        """
        获取当前会话对象
        
        Returns:
            requests.Session: 会话对象
        """
        return self.session
    
    def is_authenticated(self) -> bool:
        """
        检查是否已认证
        
        Returns:
            bool: 认证状态
        """
        return self.is_logged_in and 'JSESSIONID' in self.session.cookies
    
    def get_user_info(self) -> dict:
        """
        获取用户信息
        
        Returns:
            dict: 用户信息
        """
        return self.user_info.copy()
    
    def login_with_debug(self, username: str, password: str, captcha: str) -> Tuple[bool, str]:
        """
        带调试信息的登录方法
        """
        try:
            # 准备登录数据（根据示例代码修正字段名）
            login_data = {
                "userName": username,      # 注意：使用 userName 而不是 username
                "password": self._encrypt_password(password),
                "verifyCode": captcha,     # 注意：使用 verifyCode 而不是 verifycode
                "autoLogin": False         # 添加 autoLogin 字段
            }

            # 设置请求头，模拟真实浏览器请求
            headers = self.session.headers.copy()
            headers.update({
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': BASE_URL,
                'Referer': f'{BASE_URL}/',
                'Host': '*************'
            })

            # 调试信息
            print(f"登录请求URL: {LOGIN_URL}")
            print(f"请求数据: {login_data}")
            print(f"当前Cookie: {dict(self.session.cookies)}")

            # 发送登录请求
            response = self.session.post(
                LOGIN_URL,
                json=login_data,
                headers=headers,
                timeout=15
            )
            response.raise_for_status()

            # 调试信息
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            print(f"响应Cookie: {dict(response.cookies)}")

            # 解析响应
            result = response.json()

            # 检查响应格式：{rc: 0, ret: {...}, err: null}
            rc = result.get('rc', -1)
            ret = result.get('ret', {})
            err = result.get('err', None)

            if rc == 0 and err is None:
                self.is_logged_in = True
                self.user_info = ret
                return True, "登录成功"
            else:
                error_msg = err if err else f"登录失败 (rc: {rc})"
                return False, error_msg

        except Exception as e:
            return False, f"登录过程中发生错误: {str(e)}"

    def logout(self):
        """登出并清理会话"""
        self.is_logged_in = False
        self.user_info.clear()
        self.session.cookies.clear()
