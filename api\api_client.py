"""
API客户端 - 封装所有业务API调用
"""

import requests
import json
from typing import Optional, Dict, List, <PERSON>ple
from datetime import datetime

from config import (
    PROJECT_LIST_URL, SAMPLING_RECORDS_URL, SAMPLING_DETAIL_URL,
    EXCLUDED_FORM_NAMES
)


class APIClient:
    """API客户端类"""
    
    def __init__(self, session: requests.Session, debug_mode: bool = False):
        """
        初始化API客户端

        Args:
            session: 已认证的requests会话对象
            debug_mode: 是否启用调试模式
        """
        self.session = session
        self.debug_mode = debug_mode

        if self.debug_mode:
            print("🔧 [API] API客户端初始化完成")
    
    def get_project_info(self, keyword: str, year: int) -> Tuple[bool, Optional[Dict], str]:
        """
        获取项目基本信息

        Args:
            keyword: 项目编号（如KDHJ259560）
            year: 年份（从keyword第5-6位提取）

        Returns:
            Tu<PERSON>[bool, Optional[Dict], str]: (成功标志, 项目数据, 错误信息)
        """
        try:
            if self.debug_mode:
                print(f"📡 [API] 开始获取项目信息...")
                print(f"📡 [API] 项目编号: {keyword}")
                print(f"📡 [API] 年份: {year}")

            # 构建查询参数（根据正确的API请求格式）
            params = {
                "page": 1,
                "rows": 20,
                "keyWord": keyword,
                "departmentId": "",
                "userName": "",
                "dispatchDateBegin": "",
                "dispatchDateEnd": "",
                "predictSampleDateStart": "",
                "predictSampleDateEnd": "",
                "prepareDeliveryDateStart": "",
                "prepareDeliveryDateEnd": "",
                "createTimeBegin": f"{year}-01-01",
                "createTimeEnd": f"{year}-12-31"
            }

            if self.debug_mode:
                print(f"📡 [API] 请求URL: {PROJECT_LIST_URL}")
                print(f"📡 [API] 请求参数: {params}")

            response = self.session.get(
                PROJECT_LIST_URL,
                params=params,
                timeout=15
            )
            response.raise_for_status()

            if self.debug_mode:
                print(f"📡 [API] 响应状态码: {response.status_code}")
                print(f"📡 [API] 响应内容: {response.text}")

            result = response.json()

            if self.debug_mode:
                print(f"📡 [API] 解析后的响应: {result}")

            # 检查新的响应格式：{rc: 0, ret: {...}, err: null}
            rc = result.get('rc', -1)
            ret = result.get('ret', {})
            err = result.get('err', None)

            if self.debug_mode:
                print(f"📡 [API] rc: {rc}, err: {err}")
                print(f"📡 [API] ret: {ret}")

            if rc != 0 or err is not None:
                error_msg = err if err else f"获取项目信息失败 (rc: {rc})"
                if self.debug_mode:
                    print(f"❌ [API] {error_msg}")
                return False, None, error_msg

            # 提取项目列表
            records = ret.get('rows', [])

            if self.debug_mode:
                print(f"📡 [API] 找到 {len(records)} 条记录")
                for i, record in enumerate(records):
                    print(f"📡 [API] 记录{i+1}: {record.get('missionNo', 'N/A')} - {record.get('projectName', 'N/A')}")

            if not records:
                error_msg = f"未找到项目编号为 {keyword} 的项目"
                if self.debug_mode:
                    print(f"❌ [API] {error_msg}")
                return False, None, error_msg

            # 查找完全匹配的项目
            matched_project = None
            for project in records:
                if project.get('missionNo') == keyword:
                    matched_project = project
                    break

            if not matched_project:
                error_msg = f"项目编号 {keyword} 不完全匹配"
                if self.debug_mode:
                    print(f"❌ [API] {error_msg}")
                return False, None, error_msg

            if self.debug_mode:
                print(f"✅ [API] 找到匹配项目: {matched_project.get('missionNo')} (ID: {matched_project.get('id')})")

            return True, matched_project, ""
            
        except requests.exceptions.RequestException as e:
            return False, None, f"网络请求失败: {str(e)}"
        except json.JSONDecodeError:
            return False, None, "服务器响应格式错误"
        except Exception as e:
            return False, None, f"获取项目信息时发生错误: {str(e)}"
    
    def get_sampling_records(self, miss_bill_id: str) -> Tuple[bool, List[Dict], str]:
        """
        获取采样记录清单
        
        Args:
            miss_bill_id: 项目ID
            
        Returns:
            Tuple[bool, List[Dict], str]: (成功标志, 采样记录列表, 错误信息)
        """
        try:
            if self.debug_mode:
                print(f"📡 [API] 开始获取采样记录...")
                print(f"📡 [API] 项目ID: {miss_bill_id}")

            # 根据正确的API请求格式添加完整参数
            params = {
                "samplingType": 1,
                "missBillId": miss_bill_id,
                "type": 1,
                "page": 1,
                "size": 20,
                "taskId": "",
                "isPlaceOnFile": "false"
            }

            if self.debug_mode:
                print(f"📡 [API] 请求URL: {SAMPLING_RECORDS_URL}")
                print(f"📡 [API] 请求参数: {params}")

            response = self.session.get(
                SAMPLING_RECORDS_URL,
                params=params,
                timeout=15
            )
            response.raise_for_status()

            if self.debug_mode:
                print(f"📡 [API] 响应状态码: {response.status_code}")
                print(f"📡 [API] 响应内容: {response.text}")

            result = response.json()

            # 检查新的响应格式：{rc: 0, ret: {...}, err: null}
            rc = result.get('rc', -1)
            ret = result.get('ret', {})
            err = result.get('err', None)

            if rc != 0 or err is not None:
                error_msg = err if err else f"获取采样记录失败 (rc: {rc})"
                return False, [], error_msg

            # 提取采样记录
            records = ret.get('items', [])
            
            # 过滤掉不需要的记录
            filtered_records = []
            for record in records:
                site_form_name = record.get('siteFormName', '')
                
                # 检查是否包含排除的表单名称
                should_exclude = False
                for excluded_name in EXCLUDED_FORM_NAMES:
                    if excluded_name in site_form_name:
                        should_exclude = True
                        break
                
                if not should_exclude:
                    filtered_records.append(record)
            
            return True, filtered_records, ""
            
        except requests.exceptions.RequestException as e:
            return False, [], f"网络请求失败: {str(e)}"
        except json.JSONDecodeError:
            return False, [], "服务器响应格式错误"
        except Exception as e:
            return False, [], f"获取采样记录时发生错误: {str(e)}"
    
    def get_sampling_detail(self, save_unique_key: str) -> Tuple[bool, Optional[Dict], str]:
        """
        获取详细采样信息
        
        Args:
            save_unique_key: 采样记录唯一键
            
        Returns:
            Tuple[bool, Optional[Dict], str]: (成功标志, 详细信息, 错误信息)
        """
        try:
            params = {
                "saveUniqueKey": save_unique_key
            }
            
            response = self.session.get(
                SAMPLING_DETAIL_URL,
                params=params,
                timeout=15
            )
            response.raise_for_status()
            
            result = response.json()

            # 检查新的响应格式：{rc: 0, ret: {...}, err: null}
            rc = result.get('rc', -1)
            ret = result.get('ret', {})
            err = result.get('err', None)

            if rc != 0 or err is not None:
                error_msg = err if err else f"获取详细信息失败 (rc: {rc})"
                return False, None, error_msg

            return True, ret, ""
            
        except requests.exceptions.RequestException as e:
            return False, None, f"网络请求失败: {str(e)}"
        except json.JSONDecodeError:
            return False, None, "服务器响应格式错误"
        except Exception as e:
            return False, None, f"获取详细信息时发生错误: {str(e)}"
    
    def extract_year_from_keyword(self, keyword: str) -> Optional[int]:
        """
        从项目编号中提取年份

        Args:
            keyword: 项目编号（如KDHJ259560）

        Returns:
            Optional[int]: 年份，如果提取失败返回None
        """
        try:
            if len(keyword) >= 6:
                year_str = keyword[4:6]  # 第5-6位
                year = int(year_str)

                # 根据您的说明，这两位数字是年号
                # 假设年份范围在00-99，转换为完整年份
                if year >= 0 and year <= 30:  # 00-30 假设为2000-2030
                    return 2000 + year
                elif year >= 31 and year <= 99:  # 31-99 假设为1931-1999
                    return 1900 + year
                else:
                    return None
            return None
        except (ValueError, IndexError):
            return None
