#!/usr/bin/env python3
"""
登录调试工具 - 详细分析登录失败原因
"""

import sys
import os
import requests
import hashlib
import json
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_login_step_by_step():
    """逐步调试登录过程"""
    print("=" * 60)
    print("登录调试工具 - 逐步分析")
    print("=" * 60)
    
    # 创建会话
    session = requests.Session()
    
    # 设置完整的请求头
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Connection': 'keep-alive'
    })
    
    print("\n步骤1: 获取验证码和初始Cookie")
    print("-" * 40)
    
    # 获取验证码
    timestamp = int(time.time() * 1000)
    captcha_url = f"http://*************/ehscare/system/verifycode.jpg?t={timestamp}"
    
    try:
        response = session.get(captcha_url, timeout=10)
        print(f"验证码请求状态: {response.status_code}")
        print(f"验证码响应头: {dict(response.headers)}")
        print(f"获取到的Cookie: {dict(session.cookies)}")
        
        if response.status_code == 200:
            with open("debug_captcha.jpg", "wb") as f:
                f.write(response.content)
            print(f"✓ 验证码保存成功，大小: {len(response.content)} 字节")
        else:
            print("✗ 验证码获取失败")
            return
            
    except Exception as e:
        print(f"✗ 验证码获取异常: {e}")
        return
    
    print("\n步骤2: 输入登录信息")
    print("-" * 40)
    
    username = input("用户名: ").strip()
    password = input("密码: ").strip()
    print("请查看 debug_captcha.jpg 文件中的验证码")
    captcha = input("验证码: ").strip()
    
    # MD5加密密码
    password_md5 = hashlib.md5(password.encode('utf-8')).hexdigest()
    
    print("\n步骤3: 准备登录请求")
    print("-" * 40)
    
    login_data = {
        "username": username,
        "password": password_md5,
        "verifycode": captcha
    }
    
    print(f"登录数据: {login_data}")
    print(f"当前Cookie: {dict(session.cookies)}")
    
    # 设置登录请求头
    login_headers = session.headers.copy()
    login_headers.update({
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'http://*************',
        'Referer': 'http://*************/',
        'Host': '*************'
    })
    
    print(f"登录请求头: {dict(login_headers)}")
    
    print("\n步骤4: 发送登录请求")
    print("-" * 40)
    
    login_url = "http://*************/ehscare/pms/login"
    
    try:
        response = session.post(
            login_url,
            json=login_data,
            headers=login_headers,
            timeout=15
        )
        
        print(f"登录响应状态: {response.status_code}")
        print(f"登录响应头: {dict(response.headers)}")
        print(f"登录响应Cookie: {dict(response.cookies)}")
        print(f"登录响应内容: {response.text}")
        
        # 尝试解析JSON响应
        try:
            result = response.json()
            print(f"\n解析后的响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success', False):
                print("\n✓ 登录成功！")
                print(f"用户数据: {result.get('data', {})}")
                
                # 测试后续API调用
                test_api_access(session)
            else:
                print(f"\n✗ 登录失败: {result.get('message', '未知错误')}")
                
                # 分析失败原因
                analyze_login_failure(result)
                
        except json.JSONDecodeError as e:
            print(f"\n✗ 响应不是有效的JSON: {e}")
            print(f"原始响应: {response.text}")
            
    except Exception as e:
        print(f"\n✗ 登录请求异常: {e}")
        import traceback
        traceback.print_exc()

def analyze_login_failure(result):
    """分析登录失败原因"""
    print("\n步骤5: 分析失败原因")
    print("-" * 40)
    
    message = result.get('message', '')
    code = result.get('code', '')
    
    print(f"错误代码: {code}")
    print(f"错误消息: {message}")
    
    # 常见错误分析
    if '验证码' in message:
        print("💡 建议: 验证码错误，请重新获取验证码")
    elif '用户名' in message or '密码' in message:
        print("💡 建议: 用户名或密码错误，请检查登录凭据")
    elif '过期' in message:
        print("💡 建议: 会话过期，请重新获取验证码")
    else:
        print("💡 建议: 未知错误，请检查网络连接和服务器状态")

def test_api_access(session):
    """测试API访问"""
    print("\n步骤6: 测试API访问")
    print("-" * 40)
    
    # 测试项目列表API
    try:
        api_url = "http://*************/ehscare/missionBill/pageList"
        params = {
            "keyWord": "KDHJ259560",
            "createTimeBegin": "2025-01-01",
            "createTimeEnd": "2025-12-31",
            "pageNum": 1,
            "pageSize": 10
        }
        
        response = session.get(api_url, params=params, timeout=15)
        print(f"API测试状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                print("✓ API访问成功，登录状态有效")
                records = result.get('data', {}).get('records', [])
                print(f"找到 {len(records)} 条项目记录")
            else:
                print(f"✗ API访问失败: {result.get('message', '未知错误')}")
        else:
            print(f"✗ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"✗ API测试异常: {e}")

if __name__ == "__main__":
    debug_login_step_by_step()
