#!/usr/bin/env python3
"""
调试数据提取功能
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data.data_processor import DataProcessor

def debug_extract():
    """调试数据提取功能"""
    print("=" * 80)
    print("调试数据提取功能")
    print("=" * 80)
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 模拟您提供的JSON数据
    sample_json_data = {
        "rc": 0,
        "ret": {
            "formData": {
                "id": 44520,
                "checkNo": "KDHJ259560",
                "saveUniqueKey": "986a5c5a17404ddd8ce1dd5d745c18c9",
                "bailey": "SK海力士半导体（中国）有限公司",
                "surveyDate": "2025-08-02",
                "testPersonId": "48978,397",
                "testPerson": "471b3cfee8c44d538cb5a319b79531e0,455d79667c0a41f9b40c515415d57f4c",
                "checkDate": "2025-08-02"
            },
            "recordList": [
                {
                    "id": 1095359,
                    "sampleCode": "HJ2595600125",
                    "site": "C2F WWT 西北角7#\t",
                    "samplingStartTime": "2025-08-02 15:33:00",
                    "samplingEndTime": "2025-08-02 16:33:00",
                    "applianceNo": "X-047-69",
                    "itemArr": [
                        {
                            "itemName": "总悬浮颗粒物",
                            "methodName": "《环境空气 总悬浮颗粒物的测定 重量法》（HJ 1263-2022）"
                        }
                    ]
                }
            ]
        }
    }
    
    print("\n📋 直接调用_extract_sampling_info方法...")
    
    try:
        extracted_records = processor._extract_sampling_info(sample_json_data)
        
        print(f"\n✅ 提取成功!")
        print(f"提取出 {len(extracted_records)} 条记录")
        
        for i, record in enumerate(extracted_records):
            print(f"\n记录 {i+1}:")
            for key, value in record.items():
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ 提取失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_extract()
    input("\n按回车键退出...")
