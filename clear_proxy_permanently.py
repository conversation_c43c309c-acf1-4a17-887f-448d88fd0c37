#!/usr/bin/env python3
"""
永久清除代理设置工具
"""

import os
import sys
import subprocess
import winreg

def check_system_proxy():
    """检查Windows系统代理设置"""
    print("=" * 60)
    print("检查Windows系统代理设置")
    print("=" * 60)
    
    try:
        # 打开Internet设置注册表项
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                           r"Software\Microsoft\Windows\CurrentVersion\Internet Settings")
        
        try:
            proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
            print(f"代理启用状态: {proxy_enable} (0=禁用, 1=启用)")
            
            if proxy_enable:
                try:
                    proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
                    print(f"代理服务器: {proxy_server}")
                except FileNotFoundError:
                    print("未找到代理服务器设置")
        except FileNotFoundError:
            print("未找到ProxyEnable设置")
        
        winreg.Close<PERSON>ey(key)
        return proxy_enable if 'proxy_enable' in locals() else 0
        
    except Exception as e:
        print(f"检查系统代理失败: {e}")
        return 0

def clear_system_proxy():
    """清除Windows系统代理设置"""
    print("\n" + "=" * 60)
    print("清除Windows系统代理设置")
    print("=" * 60)
    
    try:
        # 禁用代理
        subprocess.run([
            'reg', 'add', 
            r'HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings',
            '/v', 'ProxyEnable', '/t', 'REG_DWORD', '/d', '0', '/f'
        ], check=True, capture_output=True)
        print("✓ 已禁用系统代理")
        
        # 清除代理服务器设置
        try:
            subprocess.run([
                'reg', 'delete',
                r'HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings',
                '/v', 'ProxyServer', '/f'
            ], check=True, capture_output=True)
            print("✓ 已清除代理服务器设置")
        except subprocess.CalledProcessError:
            print("  (代理服务器设置不存在或已清除)")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 清除系统代理失败: {e}")
        return False

def check_environment_variables():
    """检查环境变量中的代理设置"""
    print("\n" + "=" * 60)
    print("检查环境变量代理设置")
    print("=" * 60)
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    found_vars = []
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"✓ 发现: {var} = {value}")
            found_vars.append(var)
        else:
            print(f"  {var}: 未设置")
    
    return found_vars

def clear_user_environment_variables():
    """清除用户环境变量中的代理设置"""
    print("\n" + "=" * 60)
    print("清除用户环境变量代理设置")
    print("=" * 60)
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    success_count = 0
    
    for var in proxy_vars:
        try:
            # 删除用户环境变量
            subprocess.run([
                'reg', 'delete',
                r'HKCU\Environment',
                '/v', var, '/f'
            ], capture_output=True)
            print(f"✓ 已清除用户环境变量: {var}")
            success_count += 1
        except subprocess.CalledProcessError:
            print(f"  {var}: 不存在或已清除")
    
    if success_count > 0:
        print(f"\n✓ 成功清除 {success_count} 个用户环境变量")
        print("注意: 需要重启应用程序或重新登录才能完全生效")
    
    return success_count > 0

def check_system_environment_variables():
    """检查系统环境变量中的代理设置"""
    print("\n" + "=" * 60)
    print("检查系统环境变量代理设置")
    print("=" * 60)
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    found_system_vars = []
    
    try:
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment")
        
        for var in proxy_vars:
            try:
                value, _ = winreg.QueryValueEx(key, var)
                print(f"✓ 发现系统环境变量: {var} = {value}")
                found_system_vars.append(var)
            except FileNotFoundError:
                print(f"  {var}: 未设置")
        
        winreg.CloseKey(key)
        
    except Exception as e:
        print(f"检查系统环境变量失败: {e}")
    
    if found_system_vars:
        print(f"\n⚠️  发现 {len(found_system_vars)} 个系统级代理环境变量")
        print("清除系统环境变量需要管理员权限")
    
    return found_system_vars

def test_connection_after_clear():
    """清除代理后测试连接"""
    print("\n" + "=" * 60)
    print("测试连接")
    print("=" * 60)
    
    try:
        import requests
        
        # 创建新的session，确保不使用代理
        session = requests.Session()
        session.proxies = {}
        
        response = session.get("http://106.12.16.234/", timeout=10)
        print(f"✓ 连接测试成功: {response.status_code}")
        return True
        
    except Exception as e:
        print(f"✗ 连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("永久清除代理设置工具")
    print("=" * 60)
    
    # 检查当前状态
    system_proxy = check_system_proxy()
    env_vars = check_environment_variables()
    system_env_vars = check_system_environment_variables()
    
    # 询问是否清除
    if system_proxy or env_vars or system_env_vars:
        print("\n" + "=" * 60)
        print("发现代理设置，是否清除？")
        print("=" * 60)
        
        response = input("是否清除所有代理设置? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            
            # 清除系统代理
            if system_proxy:
                clear_system_proxy()
            
            # 清除用户环境变量
            if env_vars:
                clear_user_environment_variables()
            
            # 提示系统环境变量
            if system_env_vars:
                print("\n⚠️  检测到系统级环境变量，需要管理员权限清除")
                print("请以管理员身份运行此脚本，或手动清除")
            
            # 测试连接
            print("\n正在测试连接...")
            if test_connection_after_clear():
                print("\n🎉 代理清除成功！应用程序现在应该可以正常连接了")
            else:
                print("\n⚠️  代理已清除，但连接仍有问题，可能需要重启应用或系统")
            
        else:
            print("取消清除操作")
    else:
        print("\n✅ 未发现代理设置，系统应该可以正常连接")
        test_connection_after_clear()

if __name__ == "__main__":
    main()
