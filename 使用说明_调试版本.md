# 环境采样记录检查系统 - 调试版本使用说明

## 🎯 功能特点

这个版本的应用程序会在终端显示**详细的登录过程**，帮助您诊断登录问题。

## 🚀 启动方式

```bash
# 激活虚拟环境并启动应用
.\venv\Scripts\Activate.ps1
python main.py
```

## 📋 终端调试信息说明

### 1. 应用启动阶段
```
🚀 [MAIN] 显示登录对话框...
🔧 [AUTH] 认证管理器初始化完成
🔧 [AUTH] 请求头: {...}
```

### 2. 登录对话框启动
```
🚀 [LOGIN] 环境采样记录检查系统 - 登录对话框启动
🖼️ [LOGIN] 开始加载验证码...
```

### 3. 会话初始化过程
```
🔄 [AUTH] 正在初始化会话...
✓ [AUTH] 访问主页: 200
✓ [AUTH] 主页Cookie: {}
⚠️ [AUTH] 登录页面访问失败: 404  # 这是正常的
✓ [AUTH] 会话初始化完成，最终Cookie: {'JSESSIONID': '...'}
```

### 4. 验证码获取过程
```
🖼️ [AUTH] 验证码URL: http://*************/ehscare/system/verifycode.jpg?t=...
✓ [AUTH] 验证码获取成功，大小: 2845 字节
✓ [AUTH] 当前Cookie: {'JSESSIONID': '...'}
✅ [LOGIN] 验证码加载成功，大小: 2845 字节
```

### 5. 用户输入验证
```
🔐 [LOGIN] 开始登录验证...
🔐 [LOGIN] 用户名: 13861329404
🔐 [LOGIN] 密码: ********
🔐 [LOGIN] 验证码: 1234
✅ [LOGIN] 输入验证通过，开始登录...
```

### 6. 登录请求过程
```
🔐 [AUTH] 开始执行登录...
🔐 [AUTH] 登录数据:
   用户名: 13861329404
   密码MD5: 5d41402abc4b2a76b9719d911017c592
   验证码: 1234
🔐 [AUTH] 登录URL: http://*************/ehscare/pms/login
🔐 [AUTH] 当前Cookie: {'JSESSIONID': '...'}
```

### 7. 登录响应处理
```
📨 [AUTH] 登录响应状态码: 200
📨 [AUTH] 响应Cookie: {...}
📨 [AUTH] 响应内容: {"rc":0,"ret":{...},"err":null}
📋 [AUTH] 响应解析:
   返回码(rc): 0
   错误信息(err): null
   用户数据(ret): {...}
```

### 8. 登录结果
**成功时：**
```
✅ [AUTH] 登录成功!
✅ [AUTH] 用户信息: {...}
✅ [AUTH] 最终Cookie: {...}
🎉 [LOGIN] 登录成功: 登录成功
🎉 [LOGIN] 用户信息: {...}
🎉 [MAIN] 登录对话框返回成功
🎉 [MAIN] API客户端初始化完成
🎉 [MAIN] 当前用户: 杨运昊
✅ [MAIN] 主界面初始化完成，可以开始使用
```

**失败时：**
```
❌ [AUTH] 登录失败: 验证码错误
❌ [LOGIN] 登录失败: 验证码错误
🔄 [LOGIN] 重新加载验证码...
```

## 🔍 故障排除

### 常见问题和解决方案

1. **验证码获取失败**
   ```
   ❌ [AUTH] 获取验证码失败: ...
   ```
   - 检查网络连接
   - 检查服务器是否可访问

2. **登录失败 - 验证码错误**
   ```
   ❌ [AUTH] 登录失败: 验证码错误
   ```
   - 重新获取验证码
   - 仔细核对验证码输入

3. **登录失败 - 用户名密码错误**
   ```
   ❌ [AUTH] 登录失败: 用户名或密码错误
   ```
   - 检查用户名和密码是否正确
   - 确认账户未被锁定

4. **网络请求失败**
   ```
   ❌ [AUTH] 网络请求失败: ...
   ```
   - 检查网络连接
   - 检查防火墙设置
   - 检查代理配置

## 📝 调试技巧

1. **保存终端输出**
   - 复制终端中的所有调试信息
   - 可以帮助分析具体的失败原因

2. **关注关键信息**
   - Cookie是否正确获取
   - 响应状态码是否为200
   - 响应内容中的rc和err字段

3. **逐步排查**
   - 如果验证码获取失败，先解决网络问题
   - 如果登录失败，检查用户名密码和验证码
   - 如果API调用失败，检查登录状态

## 🎉 成功标志

当您看到以下信息时，说明登录成功：
```
✅ [MAIN] 主界面初始化完成，可以开始使用
```

此时您可以：
1. 在主界面输入项目编号
2. 点击"开始分析"进行数据分析
3. 查看分析结果和导出报告

## 📞 技术支持

如果遇到问题，请：
1. 保存完整的终端调试信息
2. 截图GUI界面的错误提示
3. 提供具体的错误描述

这些信息将帮助快速定位和解决问题。
