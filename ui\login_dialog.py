"""
登录对话框 - 用户认证界面
"""

import sys
import os
import json
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QPushButton, QLabel, QMessageBox,
                            QGroupBox, QProgressBar, QCheckBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QFont

from auth.auth_manager import AuthManager


class CaptchaThread(QThread):
    """验证码获取线程"""
    captcha_ready = pyqtSignal(bool, bytes, str)  # 成功标志, 图片数据, 错误信息
    
    def __init__(self, auth_manager: AuthManager):
        super().__init__()
        self.auth_manager = auth_manager
    
    def run(self):
        """获取验证码"""
        success, image_data, error_msg = self.auth_manager.get_captcha()
        self.captcha_ready.emit(success, image_data, error_msg)


class LoginThread(QThread):
    """登录线程"""
    login_finished = pyqtSignal(bool, str)  # 成功标志, 消息
    
    def __init__(self, auth_manager: AuthManager, username: str, password: str, captcha: str):
        super().__init__()
        self.auth_manager = auth_manager
        self.username = username
        self.password = password
        self.captcha = captcha
    
    def run(self):
        """执行登录"""
        success, message = self.auth_manager.login(self.username, self.password, self.captcha)
        self.login_finished.emit(success, message)


class LoginDialog(QDialog):
    """登录对话框"""
    
    def __init__(self, parent=None, debug_mode=True):
        super().__init__(parent)
        self.auth_manager = AuthManager(debug_mode=debug_mode)  # 启用调试模式
        self.captcha_thread = None
        self.login_thread = None

        # 在终端显示登录对话框启动信息
        if debug_mode:
            print("=" * 80)
            print("🚀 [LOGIN] 环境采样记录检查系统 - 登录对话框启动")
            print("=" * 80)

        self.init_ui()
        self.load_saved_credentials()  # 加载保存的登录信息
        self.load_captcha()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("系统登录")
        self.setFixedSize(400, 350)
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("环境采样记录检查系统")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 登录表单组
        login_group = QGroupBox("用户登录")
        form_layout = QFormLayout()
        
        # 用户名输入
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        form_layout.addRow("用户名:", self.username_edit)
        
        # 密码输入区域
        password_layout = QHBoxLayout()

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("请输入密码")
        password_layout.addWidget(self.password_edit)

        # 显示密码复选框
        self.show_password_cb = QCheckBox("显示密码")
        self.show_password_cb.stateChanged.connect(self.toggle_password_visibility)
        password_layout.addWidget(self.show_password_cb)

        form_layout.addRow("密码:", password_layout)
        
        # 验证码区域
        captcha_layout = QHBoxLayout()
        
        self.captcha_edit = QLineEdit()
        self.captcha_edit.setPlaceholderText("验证码")
        self.captcha_edit.setMaxLength(4)
        captcha_layout.addWidget(self.captcha_edit)
        
        self.captcha_label = QLabel()
        self.captcha_label.setFixedSize(100, 40)
        self.captcha_label.setStyleSheet("border: 1px solid gray; background-color: white;")
        self.captcha_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.captcha_label.setText("加载中...")
        captcha_layout.addWidget(self.captcha_label)
        
        self.refresh_captcha_btn = QPushButton("刷新")
        self.refresh_captcha_btn.setFixedWidth(60)
        self.refresh_captcha_btn.clicked.connect(self.load_captcha)
        captcha_layout.addWidget(self.refresh_captcha_btn)
        
        form_layout.addRow("验证码:", captcha_layout)

        # 保存登录信息复选框
        self.save_login_cb = QCheckBox("记住用户名和密码")
        form_layout.addRow("", self.save_login_cb)

        login_group.setLayout(form_layout)
        main_layout.addWidget(login_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.login_btn = QPushButton("登录")
        self.login_btn.setDefault(True)
        self.login_btn.clicked.connect(self.do_login)
        button_layout.addWidget(self.login_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: red;")
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
        
        # 绑定回车键
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.captcha_edit.setFocus)
        self.captcha_edit.returnPressed.connect(self.do_login)

    def load_captcha(self):
        """加载验证码"""
        if self.captcha_thread and self.captcha_thread.isRunning():
            return

        print("🖼️ [LOGIN] 开始加载验证码...")

        self.captcha_label.setText("加载中...")
        self.refresh_captcha_btn.setEnabled(False)

        self.captcha_thread = CaptchaThread(self.auth_manager)
        self.captcha_thread.captcha_ready.connect(self.on_captcha_ready)
        self.captcha_thread.start()

    def on_captcha_ready(self, success: bool, image_data: bytes, error_msg: str):
        """验证码加载完成"""
        self.refresh_captcha_btn.setEnabled(True)

        if success:
            print(f"✅ [LOGIN] 验证码加载成功，大小: {len(image_data)} 字节")

            # 显示验证码图片
            pixmap = QPixmap()
            pixmap.loadFromData(image_data)
            scaled_pixmap = pixmap.scaled(
                self.captcha_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.captcha_label.setPixmap(scaled_pixmap)
            self.status_label.setText("")
        else:
            print(f"❌ [LOGIN] 验证码加载失败: {error_msg}")
            self.captcha_label.setText("加载失败")
            self.status_label.setText(f"验证码加载失败: {error_msg}")

    def do_login(self):
        """执行登录"""
        # 验证输入
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        captcha = self.captcha_edit.text().strip()

        print(f"\n🔐 [LOGIN] 开始登录验证...")
        print(f"🔐 [LOGIN] 用户名: {username}")
        print(f"🔐 [LOGIN] 密码: {'*' * len(password)}")
        print(f"🔐 [LOGIN] 验证码: {captcha}")

        if not username:
            print("❌ [LOGIN] 用户名为空")
            self.show_error("请输入用户名")
            self.username_edit.setFocus()
            return

        if not password:
            print("❌ [LOGIN] 密码为空")
            self.show_error("请输入密码")
            self.password_edit.setFocus()
            return

        if not captcha:
            print("❌ [LOGIN] 验证码为空")
            self.show_error("请输入验证码")
            self.captcha_edit.setFocus()
            return

        print("✅ [LOGIN] 输入验证通过，开始登录...")

        # 禁用界面
        self.set_ui_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("正在登录...")

        # 启动登录线程
        self.login_thread = LoginThread(self.auth_manager, username, password, captcha)
        self.login_thread.login_finished.connect(self.on_login_finished)
        self.login_thread.start()

    def on_login_finished(self, success: bool, message: str):
        """登录完成"""
        self.set_ui_enabled(True)
        self.progress_bar.setVisible(False)

        if success:
            print(f"🎉 [LOGIN] 登录成功: {message}")
            print(f"🎉 [LOGIN] 用户信息: {self.auth_manager.get_user_info()}")

            # 保存登录凭据
            self.save_credentials()

            print("=" * 80)

            self.status_label.setText("")
            self.accept()  # 关闭对话框并返回成功
        else:
            print(f"❌ [LOGIN] 登录失败: {message}")
            print("🔄 [LOGIN] 重新加载验证码...")

            self.status_label.setText(f"登录失败: {message}")
            self.captcha_edit.clear()
            self.load_captcha()  # 重新加载验证码
            self.captcha_edit.setFocus()

    def set_ui_enabled(self, enabled: bool):
        """设置界面控件启用状态"""
        self.username_edit.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.captcha_edit.setEnabled(enabled)
        self.refresh_captcha_btn.setEnabled(enabled)
        self.login_btn.setEnabled(enabled)

    def show_error(self, message: str):
        """显示错误信息"""
        self.status_label.setText(message)
        QTimer.singleShot(3000, lambda: self.status_label.setText(""))  # 3秒后清除

    def get_auth_manager(self) -> AuthManager:
        """获取认证管理器"""
        return self.auth_manager

    def toggle_password_visibility(self, state):
        """切换密码显示/隐藏"""
        if state == 2:  # 选中状态
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:  # 未选中状态
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)

    def load_saved_credentials(self):
        """加载保存的登录凭据"""
        try:
            credentials_file = "login_credentials.json"
            if os.path.exists(credentials_file):
                with open(credentials_file, 'r', encoding='utf-8') as f:
                    credentials = json.load(f)

                username = credentials.get('username', '')
                password = credentials.get('password', '')
                save_login = credentials.get('save_login', False)

                if username:
                    self.username_edit.setText(username)
                if password:
                    self.password_edit.setText(password)

                self.save_login_cb.setChecked(save_login)

                if username and password:
                    print("🔑 [LOGIN] 已加载保存的登录信息")
        except Exception as e:
            print(f"⚠️ [LOGIN] 加载登录信息失败: {e}")

    def save_credentials(self):
        """保存登录凭据"""
        try:
            if self.save_login_cb.isChecked():
                credentials = {
                    'username': self.username_edit.text(),
                    'password': self.password_edit.text(),
                    'save_login': True
                }

                credentials_file = "login_credentials.json"
                with open(credentials_file, 'w', encoding='utf-8') as f:
                    json.dump(credentials, f, ensure_ascii=False, indent=2)

                print("💾 [LOGIN] 登录信息已保存")
            else:
                # 如果取消选中，删除保存的文件
                credentials_file = "login_credentials.json"
                if os.path.exists(credentials_file):
                    os.remove(credentials_file)
                    print("🗑️ [LOGIN] 已删除保存的登录信息")
        except Exception as e:
            print(f"⚠️ [LOGIN] 保存登录信息失败: {e}")

    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止正在运行的线程
        if self.captcha_thread and self.captcha_thread.isRunning():
            self.captcha_thread.terminate()
            self.captcha_thread.wait()

        if self.login_thread and self.login_thread.isRunning():
            self.login_thread.terminate()
            self.login_thread.wait()

        event.accept()
