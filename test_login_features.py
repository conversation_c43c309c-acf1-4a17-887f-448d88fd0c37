#!/usr/bin/env python3
"""
测试登录功能的新特性
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from ui.login_dialog import LoginDialog

def test_login_features():
    """测试登录功能"""
    print("=" * 80)
    print("测试登录功能的新特性")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    # 创建登录对话框
    login_dialog = LoginDialog(debug_mode=True)
    
    print("\n✅ 新功能已添加:")
    print("1. 显示密码复选框 - 可以切换密码显示/隐藏")
    print("2. 记住用户名和密码复选框 - 可以保存登录信息")
    print("3. 自动加载保存的登录信息")
    print("4. 登录成功后自动保存凭据")
    
    print("\n📋 使用说明:")
    print("1. 输入用户名和密码")
    print("2. 勾选'显示密码'可以看到明文密码")
    print("3. 勾选'记住用户名和密码'会在登录成功后保存信息")
    print("4. 下次启动时会自动填充保存的用户名和密码")
    print("5. 取消勾选'记住用户名和密码'会删除保存的信息")
    
    # 检查是否已有保存的登录信息
    credentials_file = "login_credentials.json"
    if os.path.exists(credentials_file):
        try:
            with open(credentials_file, 'r', encoding='utf-8') as f:
                credentials = json.load(f)
            print(f"\n🔑 发现已保存的登录信息:")
            print(f"   用户名: {credentials.get('username', 'N/A')}")
            print(f"   密码: {'*' * len(credentials.get('password', ''))}")
            print(f"   保存状态: {credentials.get('save_login', False)}")
        except Exception as e:
            print(f"\n⚠️ 读取保存的登录信息失败: {e}")
    else:
        print(f"\n📝 未发现保存的登录信息")
    
    print(f"\n🚀 启动登录对话框...")
    print("请在GUI中测试新功能!")
    
    # 显示对话框
    result = login_dialog.exec()
    
    if result == LoginDialog.DialogCode.Accepted:
        print("\n🎉 登录成功!")
        
        # 检查登录后是否保存了信息
        if os.path.exists(credentials_file):
            try:
                with open(credentials_file, 'r', encoding='utf-8') as f:
                    credentials = json.load(f)
                print(f"💾 登录信息已保存:")
                print(f"   用户名: {credentials.get('username', 'N/A')}")
                print(f"   密码: {'*' * len(credentials.get('password', ''))}")
            except Exception as e:
                print(f"⚠️ 读取保存的登录信息失败: {e}")
        else:
            print("📝 用户选择不保存登录信息")
    else:
        print("\n❌ 用户取消登录")
    
    app.quit()

if __name__ == "__main__":
    try:
        test_login_features()
        
        print(f"\n" + "=" * 80)
        print("登录功能测试完成!")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()
