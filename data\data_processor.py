"""
数据处理器 - 数据解析和冲突检查
"""

from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import re
from collections import defaultdict

from config import MIN_TRANSFER_TIME_MINUTES


class SamplingRecord:
    """采样记录数据类"""
    
    def __init__(self, data: Dict):
        """初始化采样记录"""
        self.sample_id = data.get('样品编号', '')
        self.site_name = data.get('采样点位名称', '')
        self.detection_factors = data.get('检测因子', '')

        # 优先使用已解析的datetime对象
        if '_start_datetime' in data and '_end_datetime' in data:
            self.start_time = data['_start_datetime']
            self.end_time = data['_end_datetime']
        else:
            self.start_time = self._parse_time(data.get('开始时间', ''))
            self.end_time = self._parse_time(data.get('结束时间', ''))

        self.equipment_id = data.get('设备编号', '')
        self.personnel = self._parse_personnel(data.get('采样人员', ''))
        self.sampling_date = data.get('采样日期', '')
        self.raw_data = data
    
    def _parse_time(self, time_str: str) -> Optional[datetime]:
        """解析时间字符串"""
        if not time_str:
            return None
        
        # 尝试多种时间格式
        time_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%H:%M:%S',
            '%H:%M'
        ]
        
        for fmt in time_formats:
            try:
                if ':' in time_str and len(time_str.split(':')) >= 2:
                    return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        
        return None
    
    def _parse_personnel(self, personnel_str: str) -> List[str]:
        """解析人员字符串，支持多种分隔符"""
        if not personnel_str:
            return []
        
        # 常见分隔符：逗号、分号、空格、中文顿号
        separators = [',', '，', ';', '；', ' ', '、']
        
        personnel_list = [personnel_str]
        for sep in separators:
            new_list = []
            for item in personnel_list:
                new_list.extend([p.strip() for p in item.split(sep) if p.strip()])
            personnel_list = new_list
        
        return list(set(personnel_list))  # 去重
    
    def is_valid(self) -> bool:
        """检查记录是否有效"""
        return (self.sample_id and 
                self.site_name and 
                self.start_time and 
                self.end_time and
                self.personnel)


class ConflictInfo:
    """冲突信息类"""
    
    def __init__(self, conflict_type: str, description: str, records: List[SamplingRecord]):
        self.type = conflict_type  # 'personnel_overlap', 'equipment_overlap', 'insufficient_transfer_time'
        self.description = description
        self.records = records
        self.severity = self._calculate_severity()
    
    def _calculate_severity(self) -> str:
        """计算冲突严重程度"""
        if self.type in ['personnel_overlap', 'equipment_overlap']:
            return '严重'
        elif self.type == 'insufficient_transfer_time':
            return '警告'
        return '一般'


class DataProcessor:
    """数据处理器类"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.sampling_records: List[SamplingRecord] = []
        self.conflicts: List[ConflictInfo] = []
    
    def parse_sampling_data(self, raw_data_list: List[Dict]) -> List[SamplingRecord]:
        """
        解析采样数据

        Args:
            raw_data_list: 原始数据列表

        Returns:
            List[SamplingRecord]: 解析后的采样记录列表
        """
        records = []

        for raw_data in raw_data_list:
            try:
                # 从每个原始数据中提取采样信息（现在返回列表）
                extracted_records = self._extract_sampling_info(raw_data)

                for extracted_data in extracted_records:
                    print(f"处理提取的数据: {extracted_data}")

                    # 跳过需要详细信息但没有详细信息的记录
                    if extracted_data.get('_needs_detail', False):
                        print("跳过需要详细信息的记录")
                        continue

                    # 创建SamplingRecord对象
                    try:
                        record = SamplingRecord(extracted_data)
                        print(f"创建的记录: sample_id={record.sample_id}, site_name={record.site_name}, start_time={record.start_time}, end_time={record.end_time}, personnel={record.personnel}")
                        print(f"记录有效性: {record.is_valid()}")

                        if record.is_valid():
                            records.append(record)
                        else:
                            print("记录无效，跳过")
                    except Exception as e:
                        print(f"创建SamplingRecord时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        continue

            except Exception as e:
                print(f"解析数据时出错: {e}")
                continue

        self.sampling_records = records
        return records
    
    def _extract_sampling_info(self, raw_data: Dict) -> List[Dict]:
        """从原始数据中提取采样信息"""
        extracted_records = []

        try:
            print(f"正在处理原始数据: {list(raw_data.keys())}")

            # 检查是否是API响应格式 {rc: 0, ret: {...}}
            if 'rc' in raw_data and 'ret' in raw_data:
                ret_data = raw_data['ret']
                print(f"发现API响应格式，ret包含: {list(ret_data.keys())}")

                # 检查ret中是否包含详细表单数据
                if 'formData' in ret_data and 'recordList' in ret_data:
                    form_data = ret_data['formData']
                    record_list = ret_data['recordList']
                    print(f"发现详细表单数据，记录数量: {len(record_list)}")
                    # 继续处理，不要在这里返回
                else:
                    print("ret中没有找到formData和recordList")
                    return extracted_records
            # 检查是否是直接的详细表单数据格式
            elif 'formData' in raw_data and 'recordList' in raw_data:
                # 这是详细表单数据
                form_data = raw_data['formData']
                record_list = raw_data['recordList']
                print(f"发现直接的详细表单数据，记录数量: {len(record_list)}")
            else:
                # 检查是否是采样记录清单格式
                if 'users' in raw_data and 'samplingDate' in raw_data:
                    # 这是采样记录清单数据
                    users = raw_data.get('users', '')
                    sampling_date = raw_data.get('samplingDate', '')
                    site_form_name = raw_data.get('siteFormName', '')

                    # 这种格式没有详细的时间信息，跳过或者标记为需要进一步获取详细信息
                    extracted_record = {
                        '样品编号': '',  # 需要从详细信息中获取
                        '采样点位名称': site_form_name,
                        '检测因子': '',  # 需要从详细信息中获取
                        '开始时间': '',  # 需要从详细信息中获取
                        '结束时间': '',  # 需要从详细信息中获取
                        '设备编号': '',  # 需要从详细信息中获取
                        '采样人员': users,
                        '采样日期': sampling_date,
                        '_needs_detail': True  # 标记需要获取详细信息
                    }
                    extracted_records.append(extracted_record)
                    return extracted_records
                else:
                    print(f"未识别的数据格式: {list(raw_data.keys())}")
                    return extracted_records

            # 如果到达这里，说明我们有form_data和record_list需要处理

                # 从formData中提取基本信息
                survey_date = form_data.get('surveyDate', '')
                test_person = form_data.get('testPerson', '')

                # 解析采样人员ID到姓名的映射（这里需要根据实际情况调整）
                # 暂时使用testPerson字段，实际可能需要通过其他API获取人员姓名
                personnel_list = self._extract_personnel_from_ids(test_person)
                print(f"解析出的人员列表: {personnel_list}")

                # 处理每条采样记录
                print(f"开始处理 {len(record_list)} 条记录...")
                for i, record in enumerate(record_list):
                    print(f"处理记录 {i+1}: {record.get('sampleCode', 'N/A')}")

                    sample_code = record.get('sampleCode', '')
                    site = record.get('site', '').strip()
                    sampling_start_time = record.get('samplingStartTime', '')
                    sampling_end_time = record.get('samplingEndTime', '')
                    appliance_no = record.get('applianceNo', '')

                    print(f"  样品编号: {sample_code}")
                    print(f"  采样点位: {site}")
                    print(f"  开始时间: {sampling_start_time}")
                    print(f"  结束时间: {sampling_end_time}")
                    print(f"  设备编号: {appliance_no}")

                    # 提取检测因子
                    detection_factors = []
                    item_arr = record.get('itemArr', [])
                    for item in item_arr:
                        item_name = item.get('itemName', '')
                        if item_name:
                            detection_factors.append(item_name)

                    print(f"  检测因子: {detection_factors}")

                    # 解析时间
                    start_time = self._parse_datetime(sampling_start_time)
                    end_time = self._parse_datetime(sampling_end_time)

                    print(f"  解析后开始时间: {start_time}")
                    print(f"  解析后结束时间: {end_time}")

                    if sample_code and site and start_time and end_time:
                        print("  ✅ 记录有效，添加到结果中")
                        extracted_record = {
                            '样品编号': sample_code,
                            '采样点位名称': site,
                            '检测因子': ', '.join(detection_factors),
                            '开始时间': start_time.strftime('%H:%M') if start_time else '',
                            '结束时间': end_time.strftime('%H:%M') if end_time else '',
                            '设备编号': appliance_no,
                            '采样人员': ', '.join(personnel_list),
                            '采样日期': survey_date,
                            # 保存原始时间对象用于冲突检查
                            '_start_datetime': start_time,
                            '_end_datetime': end_time
                        }
                        extracted_records.append(extracted_record)
                    else:
                        print(f"  ❌ 记录无效，跳过 (sample_code={bool(sample_code)}, site={bool(site)}, start_time={bool(start_time)}, end_time={bool(end_time)})")

        except Exception as e:
            print(f"提取采样信息时出错: {e}")
            import traceback
            traceback.print_exc()

        return extracted_records

    def _extract_personnel_from_ids(self, test_person_ids: str) -> List[str]:
        """从人员ID字符串中提取人员列表"""
        # 这里需要根据实际的人员ID到姓名的映射来实现
        # 暂时返回一个占位符，实际使用时可能需要调用其他API获取人员姓名
        if not test_person_ids:
            return []

        # 简单分割ID字符串
        id_list = test_person_ids.split(',')
        # 这里应该通过API获取真实姓名，暂时使用ID作为占位符
        return [f"人员{i+1}" for i in range(len(id_list))]

    def _parse_datetime(self, datetime_str: str) -> Optional[datetime]:
        """解析日期时间字符串"""
        if not datetime_str:
            return None

        try:
            # 尝试解析 "2025-08-02 15:33:00" 格式
            return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                # 尝试其他可能的格式
                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M')
            except ValueError:
                return None
    
    def check_personnel_conflicts(self, records: List[SamplingRecord] = None) -> List[ConflictInfo]:
        """
        检查人员时间冲突
        
        Args:
            records: 采样记录列表，如果为None则使用实例中的记录
            
        Returns:
            List[ConflictInfo]: 人员冲突列表
        """
        if records is None:
            records = self.sampling_records
        
        conflicts = []
        
        # 按人员分组
        personnel_records = defaultdict(list)
        for record in records:
            for person in record.personnel:
                personnel_records[person].append(record)
        
        # 检查每个人员的时间冲突
        for person, person_records in personnel_records.items():
            # 按时间排序
            person_records.sort(key=lambda r: r.start_time or datetime.min)
            
            for i in range(len(person_records)):
                for j in range(i + 1, len(person_records)):
                    record1, record2 = person_records[i], person_records[j]
                    
                    if not (record1.start_time and record1.end_time and 
                           record2.start_time and record2.end_time):
                        continue
                    
                    # 检查时间重叠
                    if self._times_overlap(record1.start_time, record1.end_time,
                                         record2.start_time, record2.end_time):
                        conflicts.append(ConflictInfo(
                            'personnel_overlap',
                            f'人员 {person} 在 {record1.site_name} 和 {record2.site_name} 存在时间重叠',
                            [record1, record2]
                        ))
                    
                    # 检查转移时间是否充足
                    elif record1.site_name != record2.site_name:
                        time_gap = (record2.start_time - record1.end_time).total_seconds() / 60
                        if 0 < time_gap < MIN_TRANSFER_TIME_MINUTES:
                            conflicts.append(ConflictInfo(
                                'insufficient_transfer_time',
                                f'人员 {person} 从 {record1.site_name} 到 {record2.site_name} 转移时间不足 ({time_gap:.1f}分钟)',
                                [record1, record2]
                            ))
        
        return conflicts
    
    def check_equipment_conflicts(self, records: List[SamplingRecord] = None) -> List[ConflictInfo]:
        """
        检查设备时间冲突
        
        Args:
            records: 采样记录列表，如果为None则使用实例中的记录
            
        Returns:
            List[ConflictInfo]: 设备冲突列表
        """
        if records is None:
            records = self.sampling_records
        
        conflicts = []
        
        # 按设备分组
        equipment_records = defaultdict(list)
        for record in records:
            if record.equipment_id:
                equipment_records[record.equipment_id].append(record)
        
        # 检查每个设备的时间冲突
        for equipment, equip_records in equipment_records.items():
            # 按时间排序
            equip_records.sort(key=lambda r: r.start_time or datetime.min)
            
            for i in range(len(equip_records)):
                for j in range(i + 1, len(equip_records)):
                    record1, record2 = equip_records[i], equip_records[j]
                    
                    if not (record1.start_time and record1.end_time and 
                           record2.start_time and record2.end_time):
                        continue
                    
                    # 检查时间重叠
                    if self._times_overlap(record1.start_time, record1.end_time,
                                         record2.start_time, record2.end_time):
                        conflicts.append(ConflictInfo(
                            'equipment_overlap',
                            f'设备 {equipment} 在 {record1.site_name} 和 {record2.site_name} 存在时间重叠',
                            [record1, record2]
                        ))
        
        return conflicts
    
    def _times_overlap(self, start1: datetime, end1: datetime, 
                      start2: datetime, end2: datetime) -> bool:
        """检查两个时间段是否重叠"""
        return start1 < end2 and start2 < end1
    
    def generate_timeline(self, records: List[SamplingRecord] = None) -> Dict:
        """
        生成时间线数据
        
        Args:
            records: 采样记录列表，如果为None则使用实例中的记录
            
        Returns:
            Dict: 时间线数据
        """
        if records is None:
            records = self.sampling_records
        
        timeline = {
            'personnel_timeline': defaultdict(lambda: defaultdict(list)),
            'equipment_timeline': defaultdict(lambda: defaultdict(list)),
            'daily_summary': defaultdict(lambda: {'personnel_count': 0, 'equipment_count': 0, 'sample_count': 0})
        }
        
        # 生成人员时间线
        for record in records:
            date_str = record.sampling_date or (record.start_time.strftime('%Y-%m-%d') if record.start_time else 'Unknown')
            
            for person in record.personnel:
                timeline['personnel_timeline'][person][date_str].append({
                    'start_time': record.start_time.strftime('%H:%M') if record.start_time else '',
                    'end_time': record.end_time.strftime('%H:%M') if record.end_time else '',
                    'site_name': record.site_name,
                    'detection_factors': record.detection_factors,
                    'equipment_id': record.equipment_id,
                    'sample_id': record.sample_id
                })
            
            # 生成设备时间线
            if record.equipment_id:
                timeline['equipment_timeline'][record.equipment_id][date_str].append({
                    'start_time': record.start_time.strftime('%H:%M') if record.start_time else '',
                    'end_time': record.end_time.strftime('%H:%M') if record.end_time else '',
                    'site_name': record.site_name,
                    'detection_factors': record.detection_factors,
                    'personnel': ', '.join(record.personnel),
                    'sample_id': record.sample_id
                })
            
            # 更新日汇总
            timeline['daily_summary'][date_str]['sample_count'] += 1
        
        # 计算每日人员和设备数量
        for date_str in timeline['daily_summary']:
            personnel_set = set()
            equipment_set = set()
            
            for person, person_timeline in timeline['personnel_timeline'].items():
                if date_str in person_timeline:
                    personnel_set.add(person)
            
            for equipment, equip_timeline in timeline['equipment_timeline'].items():
                if date_str in equip_timeline:
                    equipment_set.add(equipment)
            
            timeline['daily_summary'][date_str]['personnel_count'] = len(personnel_set)
            timeline['daily_summary'][date_str]['equipment_count'] = len(equipment_set)
        
        return timeline
