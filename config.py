"""
配置文件 - 环境采样记录检查应用
"""

# API配置
BASE_URL = "http://106.12.16.234"
CAPTCHA_URL = f"{BASE_URL}/ehscare/system/verifycode.jpg"
LOGIN_URL = f"{BASE_URL}/ehscare/pms/login"

# 业务API端点
PROJECT_LIST_URL = f"{BASE_URL}/ehscare/missionBill/pageList"
SAMPLING_RECORDS_URL = f"{BASE_URL}/ehscare/missionBill/getSamplingRecords"
SAMPLING_DETAIL_URL = f"{BASE_URL}/ehscare/airPollutionSampling/detail"

# HTTP请求配置
REQUEST_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json;charset=UTF-8'
}

# 时间冲突检查配置
MIN_TRANSFER_TIME_MINUTES = 2  # 人员/设备转移最小时间间隔（分钟）

# 应用配置
APP_NAME = "环境采样记录检查系统"
APP_VERSION = "1.0.0"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800

# 报告配置
REPORT_SHEET_NAMES = {
    'personnel_timeline': '人员时间流报告',
    'equipment_timeline': '设备时间流报告',
    'conflicts': '冲突分析报告',
    'summary': '汇总统计'
}

# 过滤规则
EXCLUDED_FORM_NAMES = ["环境现场检测点位示意图"]
