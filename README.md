# 环境采样记录检查系统

## 项目简介

环境采样记录检查系统是一个专业的桌面应用程序，用于分析和验证环境采样过程中人员和设备的时间安排，自动识别时间冲突和异常情况。

## 主要功能

### 1. 数据获取
- 通过API自动获取项目采样数据
- 支持三步数据获取流程：项目信息 → 采样记录 → 详细信息
- 智能年份提取和数据过滤

### 2. 冲突检查
- **人员时间冲突检查**：检测同一人员在不同点位的时间重叠
- **设备时间冲突检查**：检测同一设备在不同点位的时间重叠
- **转移时间验证**：确保人员/设备在不同点位间有足够的转移时间（默认2分钟）

### 3. 报告生成
- **人员时间流报告**：按人员和日期展示详细活动时间线
- **设备时间流报告**：按设备和日期展示使用情况
- **冲突分析报告**：列出所有发现的时间冲突
- **统计汇总报告**：提供整体统计数据

### 4. 用户界面
- 现代化的PyQt6桌面界面
- 直观的登录认证系统（支持验证码）
- 实时进度显示和状态更新
- 多标签页结果展示
- 一键导出Excel报告

## 技术架构

### 核心模块
```
environmental_sampling_checker/
├── main.py                 # 应用入口
├── config.py              # 配置常量
├── auth/
│   └── auth_manager.py    # 认证管理（登录、Cookie管理）
├── api/
│   └── api_client.py      # API客户端（数据获取）
├── data/
│   └── data_processor.py  # 数据处理（解析、冲突检查）
├── reports/
│   └── report_generator.py # 报告生成（Excel导出）
└── ui/
    ├── main_window.py     # 主窗口界面
    └── login_dialog.py    # 登录对话框
```

### 技术栈
- **界面框架**：PyQt6
- **HTTP客户端**：requests
- **数据处理**：pandas
- **Excel生成**：openpyxl
- **时间处理**：python-dateutil

## 安装和运行

### 1. 环境要求
- Python 3.8+
- Windows 10/11（推荐）

### 2. 安装依赖
```bash
pip install PyQt6 requests openpyxl pandas python-dateutil
```

或使用requirements.txt：
```bash
pip install -r requirements.txt
```

### 3. 运行应用
```bash
python main.py
```

### 4. 核心功能测试
如果遇到依赖问题，可以先运行核心功能测试：
```bash
python test_core_functionality.py
```

## 使用指南

### 1. 系统登录
- 启动应用后会自动弹出登录对话框
- 输入用户名、密码和验证码
- 验证码可点击"刷新"按钮重新获取

### 2. 项目分析
- 在主界面输入项目编号（如：KDHJ259560）
- 点击"开始分析"按钮
- 系统会自动：
  - 提取年份信息
  - 获取项目基本信息
  - 获取采样记录清单
  - 获取详细采样信息
  - 执行冲突检查
  - 生成分析报告

### 3. 查看结果
- **冲突分析**：查看所有发现的时间冲突
- **人员时间流**：查看每个人员的详细活动安排
- **设备时间流**：查看每个设备的使用情况
- **统计汇总**：查看整体统计数据

### 4. 导出报告
- 点击"导出报告"按钮
- 选择保存位置和文件名
- 系统会生成包含所有分析结果的Excel文件

## API配置

### 服务器地址
- 基础URL：`http://106.12.16.234`
- 登录端点：`/ehscare/pms/login`
- 验证码端点：`/ehscare/system/verifycode.jpg`

### 业务API端点
- 项目列表：`/ehscare/missionBill/pageList`
- 采样记录：`/ehscare/missionBill/getSamplingRecords`
- 详细信息：`/ehscare/airPollutionSampling/detail`

## 冲突检查规则

### 人员冲突
1. **时间重叠**：同一人员不能同时出现在两个不同点位
2. **转移时间**：人员在不同点位间的时间间隔不能少于2分钟

### 设备冲突
1. **时间重叠**：同一设备不能同时出现在两个不同点位
2. **转移时间**：设备在不同点位间需要有合理的转移时间

## 故障排除

### 常见问题

1. **PyQt6安装失败**
   - 尝试使用国内镜像源：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyQt6`
   - 或先运行核心功能测试验证其他模块

2. **登录失败**
   - 检查网络连接
   - 确认用户名密码正确
   - 重新获取验证码

3. **数据获取失败**
   - 检查项目编号格式
   - 确认登录状态有效
   - 检查API服务器连接

4. **报告导出失败**
   - 确认有写入权限
   - 检查磁盘空间
   - 关闭可能占用文件的Excel程序

### 日志和调试
- 应用会在状态区域显示详细的操作日志
- 错误信息会通过弹窗提示
- 可以通过控制台查看详细的错误堆栈

## 开发信息

- **版本**：1.0.0
- **开发者**：AI Assistant
- **开发时间**：2025年1月
- **许可证**：MIT License

## 更新日志

### v1.0.0 (2025-01-01)
- 初始版本发布
- 实现完整的采样记录检查功能
- 支持人员和设备时间冲突检查
- 提供专业的Excel报告导出
- 现代化的PyQt6用户界面
