#!/usr/bin/env python3
"""
环境采样记录检查应用 - 主入口
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import APP_NAME, APP_VERSION
from ui.main_window import MainWindow


def main():
    """应用程序主入口"""
    # 创建QApplication实例
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    
    # 设置应用程序属性（PyQt6中这些属性已经默认启用）
    # app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    try:
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        # 启动事件循环
        sys.exit(app.exec())
        
    except Exception as e:
        # 显示错误消息
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Icon.Critical)
        error_msg.setWindowTitle("应用程序错误")
        error_msg.setText(f"应用程序启动失败：\n{str(e)}")
        error_msg.exec()
        sys.exit(1)


if __name__ == "__main__":
    main()
