环境采样记录检查系统 - 快速开始指南
==========================================

🎉 恭喜！您的环境采样记录检查系统已经安装完成！

📁 项目结构
-----------
✓ 虚拟环境已创建 (venv/)
✓ 所有依赖包已安装 (PyQt6, requests, openpyxl, pandas等)
✓ 核心功能测试通过 (5/5)
✓ 应用程序可以正常启动

🚀 启动方式
-----------
方式1: 双击批处理文件
   activate_and_run.bat

方式2: 使用PowerShell脚本
   activate_and_run.ps1

方式3: 手动启动
   1. 打开命令行/PowerShell
   2. 进入项目目录
   3. 激活虚拟环境: venv\Scripts\activate
   4. 运行应用: python main.py

方式4: 智能启动器
   python start.py

🔧 功能测试
-----------
如果遇到问题，可以运行核心功能测试：
   python test_core_functionality.py

📖 使用说明
-----------
1. 启动应用后会弹出登录对话框
2. 输入用户名、密码和验证码登录
3. 在主界面输入项目编号（如：KDHJ259560）
4. 点击"开始分析"进行数据分析
5. 查看分析结果（冲突分析、人员时间流、设备时间流等）
6. 点击"导出报告"生成Excel文件

🔗 API配置
----------
服务器地址: http://*************
登录端点: /ehscare/pms/login
验证码端点: /ehscare/system/verifycode.jpg

📋 主要功能
-----------
✓ 用户登录认证（支持验证码）
✓ 项目数据自动获取
✓ 人员时间冲突检查
✓ 设备时间冲突检查
✓ 转移时间验证（2分钟规则）
✓ 专业Excel报告生成
✓ 现代化图形界面

❓ 故障排除
-----------
1. 如果应用无法启动：
   - 确保虚拟环境已激活
   - 运行: python test_core_functionality.py

2. 如果登录失败：
   - 检查网络连接
   - 确认用户名密码正确
   - 重新获取验证码

3. 如果数据获取失败：
   - 检查项目编号格式
   - 确认登录状态有效

📞 技术支持
-----------
详细文档: README.md
开发信息: 基于Python + PyQt6开发
版本: 1.0.0

🎯 下一步
---------
您现在可以：
1. 双击 activate_and_run.bat 启动应用
2. 使用真实的用户名密码登录系统
3. 输入项目编号开始分析
4. 导出专业的分析报告

祝您使用愉快！ 🎉
